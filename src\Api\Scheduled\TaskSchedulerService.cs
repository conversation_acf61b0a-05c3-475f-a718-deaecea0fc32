using Api.Data;
using Api.Services;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.Messages;

namespace Api.Scheduled;

public class TaskSchedulerService(IServiceProvider serviceProvider, ILogger<TaskSchedulerService> logger) : BackgroundService
{
    private readonly TimeSpan _schedulingInterval = TimeSpan.FromMinutes(5);

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        logger.LogInformation("任务调度服务已启动");

        while (!stoppingToken.IsCancellationRequested)
            try
            {
                await SchedulePendingTasksAsync();
                await Task.Delay(_schedulingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "任务调度循环中发生错误");
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }

        logger.LogInformation("任务调度服务已停止");
    }

    private async Task SchedulePendingTasksAsync()
    {
        using var scope = serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var publishEndpoint = scope.ServiceProvider.GetRequiredService<IPublishEndpoint>();
        var workerService = scope.ServiceProvider.GetRequiredService<WorkerService>();

        try
        {
            var workersResult = await workerService.GetWorkerListAsync();
            if (!workersResult.IsSuccess || workersResult.Data!.Count == 0)
            {
                logger.LogDebug("无可用工作节点，跳过调度");
                return;
            }

            var availableWorkers = workersResult.Data.Where(w => w.Status == WorkerStatus.Online).ToList();
            if (availableWorkers.Count == 0)
            {
                logger.LogDebug("无在线工作节点，跳过调度");
                return;
            }

            // 简化容量计算，假设每个在线工作节点可以处理1个任务
            var totalCapacity = availableWorkers.Count;
            if (totalCapacity <= 0)
            {
                logger.LogDebug("所有工作节点已满载，跳过调度");
                return;
            }

            var pendingTasks = await dbContext.WorkerTasks.Where(wt => wt.Status == WorkerTaskStatus.Pending).OrderBy(wt => wt.Priority)
                .ThenBy(wt => wt.CreatedAt).Take(totalCapacity).ToListAsync();

            if (!pendingTasks.Any())
            {
                logger.LogDebug("无待处理任务需要调度");
                return;
            }

            var scheduledCount = 0;
            foreach (var task in pendingTasks)
                try
                {
                    var taskMessage = new TaskMessage(task.Id, task.UserId, task.TaskType, task.Priority, task.VideoId, task.VideoUrl, task.OutputFormat,
                        task.Quality, task.StartTime, task.EndTime, task.CreatedAt);

                    await publishEndpoint.Publish<ITaskMessage>(taskMessage);

                    task.Status = WorkerTaskStatus.Queued;
                    task.UpdatedAt = DateTime.UtcNow;
                    scheduledCount++;

                    logger.LogInformation("已调度任务: {TaskId}", task.Id);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "调度任务 {TaskId} 时发生错误", task.Id);
                }

            if (scheduledCount > 0)
            {
                await dbContext.SaveChangesAsync();
                logger.LogInformation("已调度 {Count} 个任务到消息队列", scheduledCount);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "调度待处理任务时发生错误");
        }
    }

    public async Task<ServiceResult> ScheduleTaskAsync(Guid taskId)
    {
        using var scope = serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var publishEndpoint = scope.ServiceProvider.GetRequiredService<IPublishEndpoint>();

        try
        {
            var task = await dbContext.WorkerTasks.Where(wt => wt.Id == taskId && wt.Status == WorkerTaskStatus.Pending).FirstOrDefaultAsync();

            if (task == null)
                return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "任务不存在或状态不正确"));

            var taskMessage = new TaskMessage(task.Id, task.UserId, task.TaskType, task.Priority, task.VideoId, task.VideoUrl, task.OutputFormat, task.Quality,
                task.StartTime, task.EndTime, task.CreatedAt);

            await publishEndpoint.Publish<ITaskMessage>(taskMessage);

            task.Status = WorkerTaskStatus.Queued;
            task.UpdatedAt = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();

            logger.LogInformation("已手动调度任务: {TaskId}", taskId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "手动调度任务 {TaskId} 时发生错误", taskId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "调度任务失败"));
        }
    }

    public async Task<ServiceResult<object>> GetSchedulingStatsAsync()
    {
        using var scope = serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        try
        {
            var stats = await dbContext.WorkerTasks.GroupBy(wt => 1).Select(g => new
            {
                TotalTasks = g.Count(),
                PendingTasks = g.Count(wt => wt.Status == WorkerTaskStatus.Pending),
                QueuedTasks = g.Count(wt => wt.Status == WorkerTaskStatus.Queued),
                ProcessingTasks = g.Count(wt => wt.Status == WorkerTaskStatus.Processing),
                CompletedTasks = g.Count(wt => wt.Status == WorkerTaskStatus.Completed),
                FailedTasks = g.Count(wt => wt.Status == WorkerTaskStatus.Failed),
                CancelledTasks = g.Count(wt => wt.Status == WorkerTaskStatus.Cancelled)
            }).FirstOrDefaultAsync();

            return ServiceResult<object>.Success(stats ?? new
            {
                TotalTasks = 0,
                PendingTasks = 0,
                QueuedTasks = 0,
                ProcessingTasks = 0,
                CompletedTasks = 0,
                FailedTasks = 0,
                CancelledTasks = 0
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取调度统计时发生错误");
            return ServiceResult<object>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "获取调度统计失败"));
        }
    }
}