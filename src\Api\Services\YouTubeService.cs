using System.Text.Json;
using Api.Data;
using Api.Data.Models;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class YouTubeService(AppDbContext dbContext, ILogger<YouTubeService> logger, WorkerService workerService, CacheService cacheService)
{
    private readonly TimeSpan _defaultExpiration = TimeSpan.FromMinutes(30);

    public async Task<ServiceResult<YouTubeVideoResponse>> GetVideoInfoAsync(string videoId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(videoId))
            {
                return ServiceResult<YouTubeVideoResponse>.Failure(new Error(ErrorType.Validation, ErrorKeys.VALIDATION_FAILED, DeveloperMessage: "视频ID不能为空"));
            }

            if (!IsValidVideoId(videoId))
            {
                return ServiceResult<YouTubeVideoResponse>.Failure(new Error(ErrorType.Validation, ErrorKeys.VALIDATION_FAILED, DeveloperMessage: "视频ID格式不正确"));
            }

            // 检查视频是否在黑名单中
            var blacklistedVideo = await dbContext.YouTubeVideos
                .AsNoTracking()
                .FirstOrDefaultAsync(v => v.VideoId == videoId && v.IsBlacklisted);

            if (blacklistedVideo != null)
            {
                logger.LogWarning("视频在黑名单中，拒绝访问: {VideoId}", videoId);
                return ServiceResult<YouTubeVideoResponse>.Failure(new Error(ErrorType.Forbidden, ErrorKeys.NOT_FOUND, DeveloperMessage: "视频不存在或不可访问"));
            }

            var cacheKey = GetVideoKey(videoId);

            var cachedResponse = await cacheService.GetAsync<YouTubeVideoResponse>(cacheKey);
            if (cachedResponse != null)
            {
                logger.LogDebug("从缓存获取视频信息: {VideoId}", videoId);
                return ServiceResult<YouTubeVideoResponse>.Success(cachedResponse);
            }

            var existingVideo = await dbContext.YouTubeVideos
                .AsNoTracking()
                .FirstOrDefaultAsync(v => v.VideoId == videoId && !v.IsBlacklisted &&
                    (v.ExpiresAt == null || v.ExpiresAt > DateTime.UtcNow));

            if (existingVideo != null)
            {
                logger.LogDebug("从数据库获取视频信息: {VideoId}", videoId);
                var response = MapToVideoResponse(existingVideo);
                await cacheService.SetAsync(cacheKey, response, _defaultExpiration);
                return ServiceResult<YouTubeVideoResponse>.Success(response);
            }

            logger.LogInformation("从工作节点获取新视频信息: {VideoId}", videoId);
            var workerResult = await workerService.FetchYouTubeVideoAsync(videoId);

            if (!workerResult.IsSuccess)
            {
                logger.LogError("从工作节点获取视频信息失败: {VideoId}, 错误: {Error}", videoId, workerResult.Error.DeveloperMessage);
                return workerResult;
            }

            var videoResponse = workerResult.Data!;
            var videoEntity = MapToVideoEntity(videoResponse);
            dbContext.YouTubeVideos.Add(videoEntity);
            await dbContext.SaveChangesAsync();

            await cacheService.SetAsync(cacheKey, videoResponse, _defaultExpiration);

            logger.LogInformation("成功获取并缓存视频信息: {VideoId}", videoId);
            return ServiceResult<YouTubeVideoResponse>.Success(videoResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取视频信息时发生错误: {VideoId}", videoId);
            return ServiceResult<YouTubeVideoResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "获取视频信息失败"));
        }
    }

    private static string GetVideoKey(string videoId)
    {
        return $"video:{videoId}";
    }

    private static string GetPlaylistKey(string playlistId)
    {
        return $"playlist:{playlistId}";
    }

    private static string GetChannelKey(string channelId)
    {
        return $"channel:{channelId}";
    }

    private static bool IsValidVideoId(string videoId)
    {
        if (string.IsNullOrWhiteSpace(videoId) || videoId.Length != 11)
            return false;

        return videoId.All(c => char.IsLetterOrDigit(c) || c == '_' || c == '-');
    }



    private static YouTubeVideo MapToVideoEntity(YouTubeVideoResponse response)
    {
        var now = DateTime.UtcNow;

        return new YouTubeVideo
        {
            VideoId = response.VideoId,
            Title = response.Title,
            Description = response.Description,
            ChannelId = response.ChannelId,
            ChannelName = response.ChannelName,
            ChannelUrl = response.ChannelUrl,
            Duration = (int)response.Duration,
            ViewCount = response.ViewCount,
            LikeCount = response.LikeCount,
            CommentCount = response.CommentCount,
            UploadDate = response.UploadDate,
            Thumbnail = response.ThumbnailUrl,
            VideoStreamsJson = JsonSerializer.Serialize(response.VideoFormats),
            AudioStreamsJson = JsonSerializer.Serialize(response.AudioFormats),
            SubtitlesJson = JsonSerializer.Serialize(response.SubtitleFormats),
            IsBlacklisted = false,
            CreatedAt = now,
            UpdatedAt = now,
            ExpiresAt = now.AddHours(24)
        };
    }

    private async Task<YouTubeVideoResponse?> GetVideoFromDatabaseAsync(string videoId)
    {
        var video = await dbContext.YouTubeVideos.AsNoTracking().FirstOrDefaultAsync(v => v.VideoId == videoId);
        if (video == null) return null;
        if (video.ExpiresAt.HasValue && video.ExpiresAt.Value <= DateTime.UtcNow)
        {
            logger.LogDebug("视频数据已过期: {VideoId}", videoId);
            return null;
        }

        return MapToVideoResponse(video);
    }

    private async Task SaveVideoToDatabaseAsync(YouTubeVideoResponse videoResponse)
    {
        var expiresAt = DateTime.UtcNow.Add(_defaultExpiration);
        var existingVideo = await dbContext.YouTubeVideos.FirstOrDefaultAsync(v => v.VideoId == videoResponse.VideoId);

        if (existingVideo != null)
        {
            UpdateVideoEntity(existingVideo, videoResponse, expiresAt);
        }
        else
        {
            var newVideo = CreateVideoEntity(videoResponse, expiresAt);
            dbContext.YouTubeVideos.Add(newVideo);
        }

        await dbContext.SaveChangesAsync();
    }

    private async Task<YouTubePlaylistResponse?> GetPlaylistFromDatabaseAsync(string playlistId)
    {
        var playlist = await dbContext.YouTubePlaylists.AsNoTracking().FirstOrDefaultAsync(p => p.PlaylistId == playlistId);
        if (playlist == null) return null;
        if (playlist.ExpiresAt.HasValue && playlist.ExpiresAt.Value <= DateTime.UtcNow)
        {
            logger.LogDebug("播放列表数据已过期: {PlaylistId}", playlistId);
            return null;
        }

        return MapToPlaylistResponse(playlist);
    }

    private async Task SavePlaylistToDatabaseAsync(YouTubePlaylistResponse playlistResponse)
    {
        var expiresAt = DateTime.UtcNow.Add(_defaultExpiration);
        var existingPlaylist = await dbContext.YouTubePlaylists.FirstOrDefaultAsync(p => p.PlaylistId == playlistResponse.PlaylistId);

        if (existingPlaylist != null)
        {
            UpdatePlaylistEntity(existingPlaylist, playlistResponse, expiresAt);
        }
        else
        {
            var newPlaylist = CreatePlaylistEntity(playlistResponse, expiresAt);
            dbContext.YouTubePlaylists.Add(newPlaylist);
        }

        await dbContext.SaveChangesAsync();
    }

    private async Task<YouTubeChannelResponse?> GetChannelFromDatabaseAsync(string channelId)
    {
        var channel = await dbContext.YouTubeChannels.AsNoTracking().FirstOrDefaultAsync(c => c.ChannelId == channelId);
        if (channel == null) return null;
        if (channel.ExpiresAt.HasValue && channel.ExpiresAt.Value <= DateTime.UtcNow)
        {
            logger.LogDebug("频道数据已过期: {ChannelId}", channelId);
            return null;
        }

        return MapToChannelResponse(channel);
    }

    private async Task SaveChannelToDatabaseAsync(YouTubeChannelResponse channelResponse)
    {
        var expiresAt = DateTime.UtcNow.Add(_defaultExpiration);
        var existingChannel = await dbContext.YouTubeChannels.FirstOrDefaultAsync(c => c.ChannelId == channelResponse.ChannelId);

        if (existingChannel != null)
        {
            UpdateChannelEntity(existingChannel, channelResponse, expiresAt);
        }
        else
        {
            var newChannel = CreateChannelEntity(channelResponse, expiresAt);
            dbContext.YouTubeChannels.Add(newChannel);
        }

        await dbContext.SaveChangesAsync();
    }

    private YouTubeVideoResponse MapToVideoResponse(YouTubeVideo video)
    {
        var videoStreams = JsonSerializer.Deserialize<List<VideoFormat>>(video.VideoStreamsJson) ?? [];
        var audioStreams = JsonSerializer.Deserialize<List<AudioFormat>>(video.AudioStreamsJson) ?? [];
        var subtitles = JsonSerializer.Deserialize<List<SubtitleFormat>>(video.SubtitlesJson) ?? [];

        return new YouTubeVideoResponse(video.VideoId, video.Title, video.ChannelId, video.ChannelName, video.ChannelUrl,
            video.Description ?? string.Empty, video.Thumbnail ?? string.Empty,
            video.Duration, video.UploadDate ?? DateTime.UtcNow, video.ViewCount ?? 0, video.LikeCount, video.CommentCount,
            videoStreams, audioStreams, subtitles, new List<string>());
    }

    private YouTubePlaylistResponse MapToPlaylistResponse(YouTubePlaylist playlist)
    {
        var videos = JsonSerializer.Deserialize<List<PlaylistVideoInfo>>(playlist.VideosJson) ?? [];
        return new YouTubePlaylistResponse(playlist.PlaylistId, playlist.Title, playlist.Description, playlist.ChannelId, playlist.ChannelName,
            playlist.VideoCount, playlist.Thumbnail, videos);
    }

    private YouTubeChannelResponse MapToChannelResponse(YouTubeChannel channel)
    {
        var videos = JsonSerializer.Deserialize<List<ChannelVideoInfo>>(channel.VideosJson) ?? [];
        return new YouTubeChannelResponse(channel.ChannelId, channel.Title, channel.Description, channel.SubscriberCount, channel.VideoCount, channel.Thumbnail,
            videos);
    }

    private YouTubeVideo CreateVideoEntity(YouTubeVideoResponse response, DateTime expiresAt)
    {
        return new YouTubeVideo
        {
            VideoId = response.VideoId,
            Title = response.Title,
            Description = response.Description,
            ChannelId = response.ChannelId,
            ChannelName = response.ChannelName,
            ChannelUrl = response.ChannelUrl,
            Duration = (int)response.Duration,
            ViewCount = response.ViewCount,
            LikeCount = response.LikeCount,
            CommentCount = response.CommentCount,
            UploadDate = response.UploadDate,
            Thumbnail = response.ThumbnailUrl,
            VideoStreamsJson = JsonSerializer.Serialize(response.VideoFormats),
            AudioStreamsJson = JsonSerializer.Serialize(response.AudioFormats),
            SubtitlesJson = JsonSerializer.Serialize(response.SubtitleFormats),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            ExpiresAt = expiresAt
        };
    }

    private void UpdateVideoEntity(YouTubeVideo entity, YouTubeVideoResponse response, DateTime expiresAt)
    {
        entity.Title = response.Title;
        entity.Description = response.Description;
        entity.ChannelName = response.ChannelName;
        entity.ChannelUrl = response.ChannelUrl;
        entity.Duration = (int)response.Duration;
        entity.ViewCount = response.ViewCount;
        entity.LikeCount = response.LikeCount;
        entity.CommentCount = response.CommentCount;
        entity.UploadDate = response.UploadDate;
        entity.Thumbnail = response.ThumbnailUrl;
        entity.VideoStreamsJson = JsonSerializer.Serialize(response.VideoFormats);
        entity.AudioStreamsJson = JsonSerializer.Serialize(response.AudioFormats);
        entity.SubtitlesJson = JsonSerializer.Serialize(response.SubtitleFormats);
        entity.ExpiresAt = expiresAt;
        entity.UpdatedAt = DateTime.UtcNow;
    }

    private YouTubePlaylist CreatePlaylistEntity(YouTubePlaylistResponse response, DateTime expiresAt)
    {
        return new YouTubePlaylist
        {
            PlaylistId = response.PlaylistId,
            Title = response.Title,
            Description = response.Description,
            ChannelId = response.ChannelId,
            ChannelName = response.ChannelName,
            VideoCount = response.VideoCount,
            Thumbnail = response.Thumbnail,
            VideosJson = JsonSerializer.Serialize(response.Videos),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            ExpiresAt = expiresAt
        };
    }

    private void UpdatePlaylistEntity(YouTubePlaylist entity, YouTubePlaylistResponse response, DateTime expiresAt)
    {
        entity.Title = response.Title;
        entity.Description = response.Description;
        entity.ChannelName = response.ChannelName;
        entity.VideoCount = response.VideoCount;
        entity.Thumbnail = response.Thumbnail;
        entity.VideosJson = JsonSerializer.Serialize(response.Videos);
        entity.ExpiresAt = expiresAt;
        entity.UpdatedAt = DateTime.UtcNow;
    }

    private YouTubeChannel CreateChannelEntity(YouTubeChannelResponse response, DateTime expiresAt)
    {
        return new YouTubeChannel
        {
            ChannelId = response.ChannelId,
            Title = response.Title,
            Description = response.Description,
            SubscriberCount = response.SubscriberCount,
            VideoCount = response.VideoCount,
            Thumbnail = response.Thumbnail,
            VideosJson = JsonSerializer.Serialize(response.Videos),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            ExpiresAt = expiresAt
        };
    }

    private void UpdateChannelEntity(YouTubeChannel entity, YouTubeChannelResponse response, DateTime expiresAt)
    {
        entity.Title = response.Title;
        entity.Description = response.Description;
        entity.SubscriberCount = response.SubscriberCount;
        entity.VideoCount = response.VideoCount;
        entity.Thumbnail = response.Thumbnail;
        entity.VideosJson = JsonSerializer.Serialize(response.Videos);
        entity.ExpiresAt = expiresAt;
        entity.UpdatedAt = DateTime.UtcNow;
    }

    public async Task<ServiceResult<ContentListResponse>> GetContentAsync(int page, int pageSize, string? type, string? search, bool? isBlacklisted)
    {
        try
        {
            var skip = (page - 1) * pageSize;
            var contentItems = new List<ContentItem>();

            if (type == null || type == "video")
            {
                var videoQuery = dbContext.YouTubeVideos.AsQueryable();

                if (!string.IsNullOrWhiteSpace(search))
                    videoQuery = videoQuery.Where(v => v.Title.Contains(search) || v.VideoId.Contains(search));

                if (isBlacklisted.HasValue)
                    videoQuery = videoQuery.Where(v => v.IsBlacklisted == isBlacklisted.Value);

                var videos = await videoQuery.OrderByDescending(v => v.CreatedAt).Skip(skip).Take(pageSize).Select(v =>
                    new ContentItem(v.VideoId, "video", v.Title, v.ChannelName, v.Thumbnail, v.IsBlacklisted, v.CreatedAt)).ToListAsync();

                contentItems.AddRange(videos);
            }

            if (type == null || type == "channel")
            {
                var channelQuery = dbContext.YouTubeChannels.AsQueryable();

                if (!string.IsNullOrWhiteSpace(search))
                    channelQuery = channelQuery.Where(c => c.Title.Contains(search) || c.ChannelId.Contains(search));

                if (isBlacklisted.HasValue)
                    channelQuery = channelQuery.Where(c => c.IsBlacklisted == isBlacklisted.Value);

                var channels = await channelQuery.OrderByDescending(c => c.CreatedAt).Skip(skip).Take(pageSize)
                    .Select(c => new ContentItem(c.ChannelId, "channel", c.Title, c.Title, c.Thumbnail, c.IsBlacklisted, c.CreatedAt)).ToListAsync();

                contentItems.AddRange(channels);
            }

            if (type == null || type == "playlist")
            {
                var playlistQuery = dbContext.YouTubePlaylists.AsQueryable();

                if (!string.IsNullOrWhiteSpace(search))
                    playlistQuery = playlistQuery.Where(p => p.Title.Contains(search) || p.PlaylistId.Contains(search));

                if (isBlacklisted.HasValue)
                    playlistQuery = playlistQuery.Where(p => p.IsBlacklisted == isBlacklisted.Value);

                var playlists = await playlistQuery.OrderByDescending(p => p.CreatedAt).Skip(skip).Take(pageSize).Select(p =>
                    new ContentItem(p.PlaylistId, "playlist", p.Title, p.ChannelName, p.Thumbnail, p.IsBlacklisted, p.CreatedAt)).ToListAsync();

                contentItems.AddRange(playlists);
            }

            var totalCount = contentItems.Count;
            var response = new ContentListResponse(contentItems, totalCount, page, pageSize);
            return ServiceResult<ContentListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取内容列表时发生错误");
            return ServiceResult<ContentListResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult> BlacklistContentAsync(string contentType, string contentId)
    {
        try
        {
            switch (contentType.ToLower())
            {
                case "video":
                    var video = await dbContext.YouTubeVideos.FindAsync(contentId);
                    if (video == null)
                        return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "视频不存在"));

                    video.IsBlacklisted = true;
                    video.UpdatedAt = DateTime.UtcNow;
                    break;

                case "channel":
                    var channel = await dbContext.YouTubeChannels.FindAsync(contentId);
                    if (channel == null)
                        return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "频道不存在"));

                    channel.IsBlacklisted = true;
                    channel.UpdatedAt = DateTime.UtcNow;
                    break;

                case "playlist":
                    var playlist = await dbContext.YouTubePlaylists.FindAsync(contentId);
                    if (playlist == null)
                        return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "播放列表不存在"));

                    playlist.IsBlacklisted = true;
                    playlist.UpdatedAt = DateTime.UtcNow;
                    break;

                default:
                    return ServiceResult.Failure(new Error(ErrorType.Validation, ErrorKeys.VALIDATION_FAILED, DeveloperMessage: "无效的内容类型"));
            }

            await dbContext.SaveChangesAsync();
            logger.LogInformation("内容已加入黑名单: {ContentType} {ContentId}", contentType, contentId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "加入黑名单时发生错误: {ContentType} {ContentId}", contentType, contentId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult> UnblacklistContentAsync(string contentType, string contentId)
    {
        try
        {
            switch (contentType.ToLower())
            {
                case "video":
                    var video = await dbContext.YouTubeVideos.FindAsync(contentId);
                    if (video == null)
                        return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "视频不存在"));

                    video.IsBlacklisted = false;
                    video.UpdatedAt = DateTime.UtcNow;
                    break;

                case "channel":
                    var channel = await dbContext.YouTubeChannels.FindAsync(contentId);
                    if (channel == null)
                        return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "频道不存在"));

                    channel.IsBlacklisted = false;
                    channel.UpdatedAt = DateTime.UtcNow;
                    break;

                case "playlist":
                    var playlist = await dbContext.YouTubePlaylists.FindAsync(contentId);
                    if (playlist == null)
                        return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "播放列表不存在"));

                    playlist.IsBlacklisted = false;
                    playlist.UpdatedAt = DateTime.UtcNow;
                    break;

                default:
                    return ServiceResult.Failure(new Error(ErrorType.Validation, ErrorKeys.VALIDATION_FAILED, DeveloperMessage: "无效的内容类型"));
            }

            await dbContext.SaveChangesAsync();
            logger.LogInformation("内容已移出黑名单: {ContentType} {ContentId}", contentType, contentId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "移出黑名单时发生错误: {ContentType} {ContentId}", contentType, contentId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }
}