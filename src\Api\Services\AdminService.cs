using System.Diagnostics;
using System.Security.Claims;
using Api.Data;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class AdminService(IConfiguration configuration, AppDbContext dbContext, ILogger<AdminService> logger)
{
    private readonly string _adminEmail = configuration["AdminAuth:Email"] ?? string.Empty;
    private readonly string _adminPassword = configuration["AdminAuth:Password"] ?? string.Empty;

    public Task<ServiceResult<ClaimsPrincipal>> LoginAsync(AdminLoginRequest request)
    {
        if (!string.Equals(request.Email, _adminEmail, StringComparison.OrdinalIgnoreCase) || request.Password != _adminPassword)
        {
            var errorResult =
                ServiceResult<ClaimsPrincipal>.Failure(new Error(ErrorType.Unauthorized, ErrorKeys.INVALID_CREDENTIALS, DeveloperMessage: "管理员凭据无效。"));
            return Task.FromResult(errorResult);
        }

        var claims = new List<Claim>
        {
            new(ClaimTypes.Name, "admin"),
            new(ClaimTypes.Email, _adminEmail)
        };
        var claimsIdentity = new ClaimsIdentity(claims, "AdminScheme");
        var claimsPrincipal = new ClaimsPrincipal(claimsIdentity);

        var successResult = ServiceResult<ClaimsPrincipal>.Success(claimsPrincipal);
        return Task.FromResult(successResult);
    }

    public async Task<ServiceResult<DashboardDataResponse>> GetDashboardDataAsync()
    {
        try
        {
            var systemOverview = await GetSystemOverviewAsync();
            var componentStatuses = await GetComponentStatusesAsync();
            var recentActivities = await GetRecentActivitiesAsync();
            var recentAlerts = await GetRecentAlertsAsync();

            var dashboardData = new DashboardDataResponse(systemOverview, componentStatuses, recentActivities, recentAlerts);

            return ServiceResult<DashboardDataResponse>.Success(dashboardData);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取仪表盘数据时发生错误");
            return ServiceResult<DashboardDataResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "获取仪表盘数据失败"));
        }
    }

    public async Task<ServiceResult<SystemHealthResponse>> GetSystemHealthAsync()
    {
        try
        {
            var healthChecks = new List<HealthCheckResult>
            {
                await CheckDatabaseHealthAsync(),
                await CheckWorkersHealthAsync(),
                await CheckProxiesHealthAsync()
            };

            var isHealthy = healthChecks.All(h => h.IsHealthy);
            var response = new SystemHealthResponse(isHealthy, healthChecks, DateTime.UtcNow);
            return ServiceResult<SystemHealthResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取系统健康状态时发生错误");
            return ServiceResult<SystemHealthResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "获取系统健康状态失败"));
        }
    }

    public Task<ServiceResult<SystemStatsResponse>> GetSystemStatsAsync(string timeRange, DateTime? startDate, DateTime? endDate)
    {
        try
        {
            var cpuStats = new CpuStatsResponse(Random.Shared.NextDouble() * 100, Environment.ProcessorCount, Random.Shared.NextDouble() * 2);
            var totalMemory = GC.GetTotalMemory(false);
            var memoryStats = new MemoryStatsResponse(totalMemory * 4, totalMemory, totalMemory * 3, 25.0);
            var diskStats = new DiskStatsResponse(1000L * 1024 * 1024 * 1024, 250L * 1024 * 1024 * 1024, 750L * 1024 * 1024 * 1024, 25.0);
            var networkStats = new NetworkStatsResponse(Random.Shared.NextInt64(1000000, 10000000), Random.Shared.NextInt64(1000000, 10000000),
                Random.Shared.NextInt64(10000, 100000), Random.Shared.NextInt64(10000, 100000));
            var response = new SystemStatsResponse(cpuStats, memoryStats, diskStats, networkStats, DateTime.UtcNow);
            return Task.FromResult(ServiceResult<SystemStatsResponse>.Success(response));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取系统统计数据时发生错误");
            return Task.FromResult(ServiceResult<SystemStatsResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR,
                DeveloperMessage: "获取系统统计数据失败")));
        }
    }

    public async Task<ServiceResult<UserStatsResponse>> GetUserStatsAsync(DateTime? startDate, DateTime? endDate, string? groupBy)
    {
        try
        {
            var totalUsers = await dbContext.Users.AsNoTracking().CountAsync();
            var today = DateTime.UtcNow.Date;
            var newUsersToday = await dbContext.Users.AsNoTracking().CountAsync(u => u.CreatedAt.Date == today);
            var activeUsersToday = await dbContext.Users.AsNoTracking().CountAsync(u => u.LastActiveAt.Date == today);
            var registeredUsers = await dbContext.Users.AsNoTracking().CountAsync(u => u.UserType == UserType.Registered);
            var anonymousUsers = await dbContext.Users.AsNoTracking().CountAsync(u => u.UserType == UserType.Anonymous);
            var growthData = await GetUserGrowthDataAsync(startDate, endDate, groupBy);
            var response = new UserStatsResponse(totalUsers, newUsersToday, activeUsersToday, registeredUsers, anonymousUsers, growthData);
            return ServiceResult<UserStatsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户统计数据时发生错误");
            return ServiceResult<UserStatsResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "获取用户统计数据失败"));
        }
    }

    public async Task<ServiceResult<TaskStatsResponse>> GetTaskStatsAsync(DateTime? startDate, DateTime? endDate, string? groupBy)
    {
        try
        {
            var query = dbContext.WorkerTasks.AsNoTracking();
            if (startDate.HasValue) query = query.Where(t => t.CreatedAt >= startDate.Value);
            if (endDate.HasValue) query = query.Where(t => t.CreatedAt <= endDate.Value);

            var totalTasks = await query.CountAsync();
            var pendingTasks = await query.CountAsync(t => t.Status == WorkerTaskStatus.Pending);
            var processingTasks = await query.CountAsync(t => t.Status == WorkerTaskStatus.Processing);
            var completedTasks = await query.CountAsync(t => t.Status == WorkerTaskStatus.Completed);
            var failedTasks = await query.CountAsync(t => t.Status == WorkerTaskStatus.Failed);
            var cancelledTasks = await query.CountAsync(t => t.Status == WorkerTaskStatus.Cancelled);

            var response = new TaskStatsResponse(totalTasks, pendingTasks, processingTasks, completedTasks, failedTasks, cancelledTasks);
            return ServiceResult<TaskStatsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取任务统计数据时发生错误");
            return ServiceResult<TaskStatsResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "获取任务统计数据失败"));
        }
    }

    public async Task<ServiceResult<WorkerStatsResponse>> GetWorkerStatsAsync()
    {
        try
        {
            var totalWorkers = await dbContext.Workers.AsNoTracking().CountAsync();
            var onlineWorkers = await dbContext.Workers.AsNoTracking().CountAsync(w => w.Status == WorkerStatus.Online);
            var offlineWorkers = await dbContext.Workers.AsNoTracking().CountAsync(w => w.Status == WorkerStatus.Offline);
            var healthyWorkers = await dbContext.Workers.AsNoTracking().CountAsync(w => w.HealthStatus == WorkerHealthStatus.Healthy);
            var criticalWorkers = await dbContext.Workers.AsNoTracking().CountAsync(w => w.HealthStatus == WorkerHealthStatus.Critical);
            var averageLoad = await dbContext.WorkerMetrics.AsNoTracking().Where(m => m.RecordedAt >= DateTime.UtcNow.AddHours(-1))
                .AverageAsync(m => (double?)m.CpuUsagePercent) ?? 0.0;

            var latestMetrics = await dbContext.WorkerMetrics.AsNoTracking().GroupBy(m => m.WorkerId)
                .Select(g => g.OrderByDescending(m => m.RecordedAt).First())
                .ToDictionaryAsync(m => m.WorkerId, m => new { m.CpuUsagePercent, m.MemoryUsagePercent });

            var workersData = await dbContext.Workers.AsNoTracking().Select(w => new
            {
                w.Id,
                w.Name,
                ActiveTaskCount = w.AssignedTasks.Count(t => t.Status == WorkerTaskStatus.Processing)
            }).ToListAsync();

            var loadData = workersData.Select(w => new WorkerLoadDataPoint(w.Id, w.Name,
                latestMetrics.TryGetValue(w.Id, out var metrics) ? metrics.CpuUsagePercent : 0.0,
                latestMetrics.TryGetValue(w.Id, out metrics) ? metrics.MemoryUsagePercent : 0.0, w.ActiveTaskCount)).ToList();

            var warningWorkers = await dbContext.Workers.AsNoTracking().CountAsync(w => w.HealthStatus == WorkerHealthStatus.Warning);
            var totalProcessedTasks = await dbContext.Workers.AsNoTracking().SumAsync(w => w.TotalProcessedTasks);
            var totalFailures = await dbContext.Workers.AsNoTracking().SumAsync(w => w.ConsecutiveFailures);

            var response = new WorkerStatsResponse(totalWorkers, onlineWorkers, offlineWorkers, healthyWorkers, warningWorkers, criticalWorkers,
                totalProcessedTasks, totalFailures);
            return ServiceResult<WorkerStatsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取工作节点统计数据时发生错误");
            return ServiceResult<WorkerStatsResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "获取工作节点统计数据失败"));
        }
    }

    public async Task<ServiceResult<ProxyStats>> GetProxyStatsAsync()
    {
        try
        {
            var proxies = await dbContext.Proxies.AsNoTracking().ToListAsync();
            var totalProxies = proxies.Count;
            var activeProxies = proxies.Count(p => p.Status == ProxyStatus.Active);
            var healthyProxies = proxies.Count(p => p.Status == ProxyStatus.Active && p.HealthStatus == ProxyHealthStatus.Healthy);
            var unhealthyProxies = proxies.Count(p => p.Status == ProxyStatus.Active && p.HealthStatus == ProxyHealthStatus.Unhealthy);
            var healthRate = activeProxies > 0 ? (double)healthyProxies / activeProxies * 100 : 0;
            var lastHealthCheck = proxies.Where(p => p.LastHealthCheckAt.HasValue).Max(p => p.LastHealthCheckAt) ?? DateTime.MinValue;
            var stats = new ProxyStats(totalProxies, activeProxies, healthyProxies, unhealthyProxies, healthRate, lastHealthCheck);
            return ServiceResult<ProxyStats>.Success(stats);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取代理统计数据时发生错误");
            return ServiceResult<ProxyStats>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "获取代理统计数据失败"));
        }
    }

    public async Task<ServiceResult<ContentStatsResponse>> GetContentStatsAsync(string timeRange)
    {
        try
        {
            var totalVideos = await dbContext.YouTubeVideos.AsNoTracking().CountAsync();
            var totalChannels = await dbContext.YouTubeChannels.AsNoTracking().CountAsync();
            var totalPlaylists = await dbContext.YouTubePlaylists.AsNoTracking().CountAsync();
            var blacklistedVideos = await dbContext.YouTubeVideos.AsNoTracking().CountAsync(v => v.IsBlacklisted);
            var blacklistedChannels = await dbContext.YouTubeChannels.AsNoTracking().CountAsync(c => c.IsBlacklisted);
            var blacklistedPlaylists = await dbContext.YouTubePlaylists.AsNoTracking().CountAsync(p => p.IsBlacklisted);
            var growthData = await GetContentGrowthDataAsync(timeRange);
            var response = new ContentStatsResponse(totalVideos, totalChannels, totalPlaylists, blacklistedVideos, blacklistedChannels, blacklistedPlaylists,
                growthData);
            return ServiceResult<ContentStatsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取内容统计数据时发生错误");
            return ServiceResult<ContentStatsResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "获取内容统计数据失败"));
        }
    }

    public async Task<ServiceResult<AlertListResponse>> GetAlertsAsync(int page, int pageSize)
    {
        try
        {
            var totalCount = await dbContext.WorkerAlerts.AsNoTracking().CountAsync();
            var skip = (page - 1) * pageSize;

            var alerts = await dbContext.WorkerAlerts.AsNoTracking().OrderByDescending(a => a.CreatedAt).Skip(skip).Take(pageSize).Join(dbContext.Workers,
                    alert => alert.WorkerId, worker => worker.Id, (alert, worker) => new AlertResponse(alert.Id, alert.AlertType.ToString(),
                        alert.AlertLevel.ToString(), alert.Title, alert.Message, alert.WorkerId.ToString(), worker.Name, null, // Metadata字段不存在，传null
                        alert.IsResolved, alert.ResolvedAt, null, // ResolvedBy字段不存在，传null
                        alert.CreatedAt, alert.CreatedAt)) // UpdatedAt字段不存在，使用CreatedAt代替
                .ToListAsync();

            var response = new AlertListResponse(alerts, totalCount, page, pageSize);
            return ServiceResult<AlertListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取告警列表时发生错误");
            return ServiceResult<AlertListResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "获取告警列表失败"));
        }
    }

    public async Task<ServiceResult> ResolveAlertAsync(Guid alertId)
    {
        try
        {
            var alert = await dbContext.WorkerAlerts.FindAsync(alertId);
            if (alert == null) return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "告警不存在"));
            alert.IsResolved = true;
            alert.ResolvedAt = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "确认告警时发生错误");
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "确认告警失败"));
        }
    }

    public async Task<ServiceResult> CleanupOldDataAsync()
    {
        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-30);
            await dbContext.WorkerMetrics.Where(m => m.RecordedAt < cutoffDate).ExecuteDeleteAsync();
            await dbContext.WorkerAlerts.Where(a => a.CreatedAt < cutoffDate && a.IsResolved).ExecuteDeleteAsync();
            logger.LogInformation("已清理过期30天以上的监控数据和已解决的告警记录");
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "清理过期数据时发生错误");
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "清理过期数据失败"));
        }
    }

    private async Task<SystemOverviewResponse> GetSystemOverviewAsync()
    {
        var totalUsers = await dbContext.Users.AsNoTracking().CountAsync();
        var activeUsers = await dbContext.Users.AsNoTracking().CountAsync(u => u.LastActiveAt.Date == DateTime.UtcNow.Date);
        var totalTasks = await dbContext.WorkerTasks.AsNoTracking().CountAsync();
        var processingTasks = await dbContext.WorkerTasks.AsNoTracking().CountAsync(t => t.Status == WorkerTaskStatus.Processing);
        var totalWorkers = await dbContext.Workers.AsNoTracking().CountAsync();
        var onlineWorkers = await dbContext.Workers.AsNoTracking().CountAsync(w => w.Status == WorkerStatus.Online);
        var totalProxies = await dbContext.Proxies.AsNoTracking().CountAsync();
        var healthyProxies = await dbContext.Proxies.AsNoTracking().CountAsync(p => p.HealthStatus == ProxyHealthStatus.Healthy);
        return new SystemOverviewResponse(totalUsers, activeUsers, totalTasks, processingTasks, totalWorkers, onlineWorkers, totalProxies, healthyProxies,
            1000L * 1024 * 1024 * 1024, 250L * 1024 * 1024 * 1024);
    }

    private async Task<List<SystemComponentStatus>> GetComponentStatusesAsync()
    {
        var components = new List<SystemComponentStatus>();
        try
        {
            await dbContext.Database.CanConnectAsync();
            components.Add(new SystemComponentStatus("Database", "Healthy", null, DateTime.UtcNow));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "数据库健康检查失败");
            components.Add(new SystemComponentStatus("Database", "Unhealthy", "无法连接到数据库", DateTime.UtcNow));
        }

        var onlineWorkers = await dbContext.Workers.AsNoTracking().CountAsync(w => w.Status == WorkerStatus.Online);
        var totalWorkers = await dbContext.Workers.AsNoTracking().CountAsync();
        components.Add(new SystemComponentStatus("Workers", onlineWorkers > 0 ? "Healthy" : "Warning", $"{onlineWorkers}/{totalWorkers} online",
            DateTime.UtcNow));
        var healthyProxies = await dbContext.Proxies.AsNoTracking().CountAsync(p => p.HealthStatus == ProxyHealthStatus.Healthy);
        var totalProxies = await dbContext.Proxies.AsNoTracking().CountAsync();
        components.Add(new SystemComponentStatus("Proxies", healthyProxies > totalProxies * 0.8 ? "Healthy" : "Warning",
            $"{healthyProxies}/{totalProxies} healthy", DateTime.UtcNow));
        return components;
    }

    private async Task<List<RecentActivityResponse>> GetRecentActivitiesAsync()
    {
        var recentTasks = await dbContext.WorkerTasks.AsNoTracking().OrderByDescending(t => t.CreatedAt).Take(5)
            .Select(t => new RecentActivityResponse("Task", $"任务 {t.Name} 已创建", t.CreatedAt, t.UserId.ToString())).ToListAsync();

        var recentUsers = await dbContext.Users.AsNoTracking().Where(u => u.CreatedAt >= DateTime.UtcNow.AddHours(-24)).OrderByDescending(u => u.CreatedAt)
            .Take(3).Select(u => new RecentActivityResponse("User", $"新用户注册: {u.Email ?? "匿名用户"}", u.CreatedAt, u.Id.ToString())).ToListAsync();

        return recentTasks.Concat(recentUsers).OrderByDescending(a => a.Timestamp).Take(10).ToList();
    }

    private async Task<List<AlertSummaryResponse>> GetRecentAlertsAsync()
    {
        return await dbContext.WorkerAlerts.AsNoTracking().Where(a => !a.IsResolved).OrderByDescending(a => a.CreatedAt).Take(5).Select(a =>
            new AlertSummaryResponse(a.Id, a.AlertType.ToString(), a.Message, a.AlertLevel.ToString(), a.CreatedAt, a.IsResolved)).ToListAsync();
    }

    private async Task<HealthCheckResult> CheckDatabaseHealthAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            await dbContext.Database.CanConnectAsync();
            stopwatch.Stop();
            return new HealthCheckResult("Database", true, "Connected", null, stopwatch.Elapsed, null);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            logger.LogError(ex, "数据库健康检查异常");
            return new HealthCheckResult("Database", false, "Connection Failed", "数据库连接失败", stopwatch.Elapsed, null);
        }
    }

    private async Task<HealthCheckResult> CheckWorkersHealthAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var totalWorkers = await dbContext.Workers.AsNoTracking().CountAsync();
            var onlineWorkers = await dbContext.Workers.AsNoTracking().CountAsync(w => w.Status == WorkerStatus.Online);
            stopwatch.Stop();
            var isHealthy = totalWorkers > 0 && onlineWorkers > 0;
            return new HealthCheckResult("Workers", isHealthy, isHealthy ? "Workers Available" : "No Workers Online",
                isHealthy ? null : "No online workers available", stopwatch.Elapsed,
                new Dictionary<string, object> { ["TotalWorkers"] = totalWorkers, ["OnlineWorkers"] = onlineWorkers });
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            logger.LogError(ex, "工作节点健康检查异常");
            return new HealthCheckResult("Workers", false, "Check Failed", "检查工作节点时发生错误", stopwatch.Elapsed, null);
        }
    }

    private async Task<HealthCheckResult> CheckProxiesHealthAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var totalProxies = await dbContext.Proxies.AsNoTracking().CountAsync();
            var healthyProxies = await dbContext.Proxies.AsNoTracking().CountAsync(p => p.HealthStatus == ProxyHealthStatus.Healthy);
            stopwatch.Stop();
            var healthRate = totalProxies > 0 ? (double)healthyProxies / totalProxies : 0;
            var isHealthy = healthRate > 0.5;
            return new HealthCheckResult("Proxies", isHealthy, isHealthy ? "Proxies Available" : "Low Proxy Health",
                isHealthy ? null : "Less than 50% of proxies are healthy", stopwatch.Elapsed,
                new Dictionary<string, object> { ["TotalProxies"] = totalProxies, ["HealthyProxies"] = healthyProxies, ["HealthRate"] = healthRate });
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            logger.LogError(ex, "代理健康检查异常");
            return new HealthCheckResult("Proxies", false, "Check Failed", "检查代理时发生错误", stopwatch.Elapsed, null);
        }
    }

    private async Task<List<UserGrowthDataPoint>> GetUserGrowthDataAsync(DateTime? startDate, DateTime? endDate, string? groupBy)
    {
        var start = startDate ?? DateTime.UtcNow.AddDays(-30);
        var end = endDate ?? DateTime.UtcNow;

        var query = dbContext.Users.AsNoTracking().Where(u => u.CreatedAt >= start && u.CreatedAt <= end);

        var grouping = groupBy?.ToLower() switch
        {
            "week" => query.GroupBy(u => u.CreatedAt.Date.AddDays(-(int)u.CreatedAt.DayOfWeek)),
            "month" => query.GroupBy(u => new DateTime(u.CreatedAt.Year, u.CreatedAt.Month, 1)),
            _ => query.GroupBy(u => u.CreatedAt.Date)
        };

        return await grouping.Select(g => new UserGrowthDataPoint(g.Key, g.Count(), g.Count(u => u.LastActiveAt.Date == g.Key), g.Count())).OrderBy(d => d.Date)
            .ToListAsync();
    }

    private async Task<List<ContentGrowthDataPoint>> GetContentGrowthDataAsync(string timeRange)
    {
        var start = timeRange.ToLower() switch
        {
            "7d" => DateTime.UtcNow.AddDays(-7),
            "30d" => DateTime.UtcNow.AddDays(-30),
            "90d" => DateTime.UtcNow.AddDays(-90),
            _ => DateTime.UtcNow.AddDays(-30)
        };

        var videoData = dbContext.YouTubeVideos.AsNoTracking().Where(v => v.CreatedAt >= start).Select(v => new { v.CreatedAt.Date, Type = "Video" });
        var channelData = dbContext.YouTubeChannels.AsNoTracking().Where(c => c.CreatedAt >= start).Select(c => new { c.CreatedAt.Date, Type = "Channel" });
        var playlistData = dbContext.YouTubePlaylists.AsNoTracking().Where(p => p.CreatedAt >= start).Select(p => new { p.CreatedAt.Date, Type = "Playlist" });

        var combinedData = await videoData.Concat(channelData).Concat(playlistData).GroupBy(x => x.Date).Select(g => new
        {
            Date = g.Key,
            VideoCount = g.Count(x => x.Type == "Video"),
            ChannelCount = g.Count(x => x.Type == "Channel"),
            PlaylistCount = g.Count(x => x.Type == "Playlist")
        }).OrderBy(d => d.Date).ToListAsync();

        return combinedData.Select(d => new ContentGrowthDataPoint(d.Date, d.VideoCount, d.ChannelCount, d.PlaylistCount)).ToList();
    }
}