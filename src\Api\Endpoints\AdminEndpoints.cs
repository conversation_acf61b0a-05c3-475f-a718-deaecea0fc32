using Api.Extensions;
using Api.Filters;
using Api.Services;
using Microsoft.AspNetCore.Authentication;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class AdminEndpoints
{
    public static void MapAdminAuthEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/auth").RequireAuthorization("AdminOnly").WithTags("Admin Auth");

        group.MapPost("/login", async (AdminLoginRequest request, HttpContext context, AdminService adminService) =>
        {
            var result = await adminService.LoginAsync(request);
            if (!result.IsSuccess) return result.ToHttpResult();

            var authProperties = new AuthenticationProperties
            {
                IsPersistent = true,
                ExpiresUtc = DateTimeOffset.UtcNow.AddHours(8)
            };

            await context.SignInAsync("AdminScheme", result.Data!, authProperties);
            return TypedResults.Ok(ApiResponse.Success());
        }).AllowAnonymous().WithSummary("管理员登录");

        group.MapPost("/logout", async (HttpContext context) =>
        {
            await context.SignOutAsync("AdminScheme");
            return TypedResults.Ok(ApiResponse.Success());
        }).WithSummary("管理员登出");

        group.MapGet("/me", () => TypedResults.Ok(ApiResponse.Success())).WithSummary("验证管理员会话");
    }

    public static void MapAdminDashboardEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/dashboard").RequireAuthorization("AdminOnly").WithTags("Admin Dashboard");

        group.MapGet("/", async (AdminService adminService) =>
        {
            var result = await adminService.GetDashboardDataAsync();
            return result.ToHttpResult();
        }).WithSummary("获取仪表盘数据");

        group.MapGet("/health", async (AdminService adminService) =>
        {
            var result = await adminService.GetSystemHealthAsync();
            return result.ToHttpResult();
        }).WithSummary("系统健康检查");

        group.MapGet("/stats/system", async (string timeRange, DateTime? startDate, DateTime? endDate, AdminService adminService) =>
        {
            var result = await adminService.GetSystemStatsAsync(timeRange, startDate, endDate);
            return result.ToHttpResult();
        }).WithSummary("获取系统统计");

        group.MapGet("/stats/users", async (DateTime? startDate, DateTime? endDate, string? groupBy, AdminService adminService) =>
        {
            var result = await adminService.GetUserStatsAsync(startDate, endDate, groupBy);
            return result.ToHttpResult();
        }).WithSummary("获取用户统计");

        group.MapGet("/stats/tasks", async (DateTime? startDate, DateTime? endDate, string? groupBy, AdminService adminService) =>
        {
            var result = await adminService.GetTaskStatsAsync(startDate, endDate, groupBy);
            return result.ToHttpResult();
        }).WithSummary("获取任务统计");

        group.MapGet("/stats/workers", async (AdminService adminService) =>
        {
            var result = await adminService.GetWorkerStatsAsync();
            return result.ToHttpResult();
        }).WithSummary("获取工作节点统计");

        group.MapGet("/stats/proxies", async (AdminService adminService) =>
        {
            var result = await adminService.GetProxyStatsAsync();
            return result.ToHttpResult();
        }).WithSummary("获取代理统计");

        group.MapGet("/stats/content", async (string timeRange, AdminService adminService) =>
        {
            var result = await adminService.GetContentStatsAsync(timeRange);
            return result.ToHttpResult();
        }).WithSummary("获取内容统计");

        group.MapGet("/alerts", async (int page, int pageSize, AdminService adminService) =>
        {
            var result = await adminService.GetAlertsAsync(page, pageSize);
            return result.ToHttpResult();
        }).WithSummary("获取系统告警");

        group.MapPost("/alerts/{alertId}/resolve", async (Guid alertId, AdminService adminService) =>
        {
            var result = await adminService.ResolveAlertAsync(alertId);
            return result.ToHttpResult();
        }).WithSummary("解决告警");

        group.MapPost("/cleanup", async (AdminService adminService) =>
        {
            var result = await adminService.CleanupOldDataAsync();
            return result.ToHttpResult();
        }).WithSummary("清理过期数据");
    }

    public static void MapAdminUserEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/users").RequireAuthorization("AdminOnly").WithTags("Admin User Management");

        group.MapGet("/", async (int page, int pageSize, string? search, UserType? userType, UserAccountStatus? status, UserService userService) =>
        {
            var result = await userService.GetUsersAsync(page, pageSize, search, userType, status);
            return result.ToHttpResult();
        }).WithSummary("获取用户列表");

        group.MapGet("/{userId}", async (Guid userId, UserService userService) =>
        {
            var result = await userService.GetUserDetailAsync(userId);
            return result.ToHttpResult();
        }).WithSummary("获取用户详情");

        group.MapPost("/{userId}/enable", async (Guid userId, UserService userService) =>
        {
            var result = await userService.UpdateUserStatusAsync(userId, UserAccountStatus.Active);
            return result.ToHttpResult();
        }).WithSummary("启用用户");

        group.MapPost("/{userId}/disable", async (Guid userId, UserService userService) =>
        {
            var result = await userService.UpdateUserStatusAsync(userId, UserAccountStatus.Disabled);
            return result.ToHttpResult();
        }).WithSummary("禁用用户");

        group.MapPost("/{userId}/unlock", async (Guid userId, UserService userService) =>
        {
            var result = await userService.UnlockUserAccountAsync(userId);
            return result.ToHttpResult();
        }).WithSummary("解锁用户账户");

        group.MapPost("/{userId}/reset-password", async (Guid userId, UserService userService) =>
        {
            var result = await userService.ResetUserPasswordAsync(userId);
            return result.ToHttpResult();
        }).WithSummary("重置用户密码");

        group.MapPost("/{userId}/modify-plan", async (Guid userId, UpdateUserPlanRequest request, UserService userService) =>
        {
            var result = await userService.UpdateUserPlanAsync(userId, request);
            return result.ToHttpResult();
        }).WithSummary("修改用户套餐");

        group.MapPost("/{userId}/sessions/revoke", async (Guid userId, UserService userService) =>
        {
            var result = await userService.RevokeUserSessionsAsync(userId);
            return result.ToHttpResult();
        }).WithSummary("撤销用户会话");

        group.MapGet("/{userId}/sessions", async (Guid userId, UserService userService) =>
        {
            var result = await userService.GetUserSessionsAsync(userId);
            return result.ToHttpResult();
        }).WithSummary("获取用户会话");

        group.MapGet("/{userId}/tasks", async (Guid userId, int page, int pageSize, UserService userService) =>
        {
            var result = await userService.GetUserTasksAsync(userId, page, pageSize);
            return result.ToHttpResult();
        }).WithSummary("获取用户任务");

        group.MapGet("/{userId}/content", async (Guid userId, int page, int pageSize, UserService userService) =>
        {
            var result = await userService.GetUserContentHistoryAsync(userId, page, pageSize);
            return result.ToHttpResult();
        }).WithSummary("获取用户内容记录");

        group.MapGet("/{userId}/billing", async (Guid userId, UserService userService) =>
        {
            var result = await userService.GetUserBillingHistoryAsync(userId);
            return result.ToHttpResult();
        }).WithSummary("获取用户计费记录");
    }

    public static void MapAdminTaskEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/tasks").RequireAuthorization("AdminOnly").WithTags("Admin Task Management");

        group.MapGet("/",
            async (int page, int pageSize, WorkerTaskStatus? status, WorkerTaskType? taskType, DateTime? startDate, DateTime? endDate,
                TaskService taskService) =>
            {
                var result = await taskService.GetTasksAsync(page, pageSize, status, taskType, startDate, endDate);
                return result.ToHttpResult();
            }).WithSummary("获取任务列表");

        group.MapGet("/{taskId}", async (Guid taskId, TaskService taskService) =>
        {
            var result = await taskService.GetTaskDetailAsync(taskId);
            return result.ToHttpResult();
        }).WithSummary("获取任务详情");

        group.MapPost("/{taskId}/cancel", async (Guid taskId, TaskService taskService) =>
        {
            var result = await taskService.CancelTaskAsync(taskId);
            return result.ToHttpResult();
        }).WithSummary("取消任务");

        group.MapPost("/{taskId}/retry", async (Guid taskId, TaskService taskService) =>
        {
            var result = await taskService.RetryTaskAsync(taskId);
            return result.ToHttpResult();
        }).WithSummary("重试任务");

        group.MapPost("/{taskId}/delete", async (Guid taskId, TaskService taskService) =>
        {
            var result = await taskService.DeleteTaskAsync(taskId);
            return result.ToHttpResult();
        }).WithSummary("删除任务记录");

        group.MapPost("/cleanup", async (int daysOld, TaskService taskService) =>
        {
            var result = await taskService.CleanupOldTasksAsync(daysOld);
            return result.ToHttpResult();
        }).WithSummary("清理过期任务");
    }

    public static void MapAdminWorkerEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/workers").RequireAuthorization("AdminOnly").WithTags("Admin Worker Management");

        group.MapPost("/register", async (WorkerRegisterRequest request, WorkerService workerService) =>
        {
            var result = await workerService.RegisterWorkerAsync(request);
            return result.ToHttpResult();
        }).WithValidation<WorkerRegisterRequest>().WithSummary("工作节点注册").AllowAnonymous();

        group.MapPost("/{workerId}/heartbeat", async (Guid workerId, WorkerHeartbeat heartbeat, WorkerService workerService) =>
        {
            var result = await workerService.UpdateHeartbeatAsync(workerId, heartbeat);
            return result.ToHttpResult();
        }).WithValidation<WorkerHeartbeat>().WithSummary("工作节点心跳").AllowAnonymous();

        group.MapPost("/{workerId}/unregister", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.UnregisterWorkerAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("工作节点注销").AllowAnonymous();

        group.MapGet("/", async (WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerListAsync();
            return result.ToHttpResult();
        }).WithSummary("获取工作节点列表");

        group.MapGet("/{workerId}", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerByIdAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("获取工作节点详情");

        group.MapGet("/{workerId}/tasks", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.GetActiveTaskCountAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("获取工作节点任务数");

        group.MapPost("/{workerId}/start", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.EnableWorkerAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("启动工作节点");

        group.MapPost("/{workerId}/stop", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.DisableWorkerAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("停止工作节点");

        group.MapPost("/{workerId}/restart", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.RestartWorkerAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("重启工作节点");

        group.MapPost("/{workerId}/health-check", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.HealthCheckWorkerAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("健康检查工作节点");

        group.MapPost("/health-check", async (WorkerService workerService) =>
        {
            var result = await workerService.HealthCheckAllWorkersAsync();
            return result.ToHttpResult();
        }).WithSummary("健康检查所有工作节点");

        group.MapPost("/{workerId}/cleanup-files", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.CleanupWorkerFilesAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("清理工作节点文件");

        group.MapPost("/cleanup-files", async (WorkerService workerService) =>
        {
            var result = await workerService.CleanupAllWorkerFilesAsync();
            return result.ToHttpResult();
        }).WithSummary("清理所有工作节点文件");

        group.MapPost("/{workerId}/delete", async (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.DeleteWorkerAsync(workerId);
            return result.ToHttpResult();
        }).WithSummary("删除工作节点");
    }

    public static void MapAdminProxyEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/proxies").RequireAuthorization("AdminOnly").WithTags("Admin Proxy Management");

        group.MapGet("/", async (int page, int pageSize, string? status, string? healthStatus, ProxyService proxyService) =>
        {
            var result = await proxyService.GetProxyListAsync(page, pageSize, status, healthStatus);
            return result.ToHttpResult();
        }).WithSummary("获取代理列表");

        group.MapGet("/{proxyId}", async (Guid proxyId, ProxyService proxyService) =>
        {
            var result = await proxyService.GetProxyDetailAsync(proxyId);
            return result.ToHttpResult();
        }).WithSummary("获取代理详情");

        group.MapPost("/", async (AddProxyRequest request, ProxyService proxyService) =>
        {
            var result = await proxyService.AddProxyAsync(request);
            return result.ToHttpResult();
        }).WithValidation<AddProxyRequest>().WithSummary("添加代理");

        group.MapPost("/batch-add", async (BatchAddProxiesRequest request, ProxyService proxyService) =>
        {
            var result = await proxyService.BatchAddProxiesAsync(request);
            return result.ToHttpResult();
        }).WithValidation<BatchAddProxiesRequest>().WithSummary("批量添加代理");

        group.MapPost("/{proxyId}/update", async (Guid proxyId, UpdateProxyRequest request, ProxyService proxyService) =>
        {
            var result = await proxyService.UpdateProxyAsync(proxyId, request);
            return result.ToHttpResult();
        }).WithValidation<UpdateProxyRequest>().WithSummary("更新代理");

        group.MapPost("/{proxyId}/delete", async (Guid proxyId, ProxyService proxyService) =>
        {
            var result = await proxyService.DeleteProxyAsync(proxyId);
            return result.ToHttpResult();
        }).WithSummary("删除代理");

        group.MapPost("/batch-delete", async (BatchDeleteProxiesRequest request, ProxyService proxyService) =>
        {
            var result = await proxyService.BatchDeleteProxiesAsync(request);
            return result.ToHttpResult();
        }).WithValidation<BatchDeleteProxiesRequest>().WithSummary("批量删除代理");

        group.MapPost("/{proxyId}/enable", async (Guid proxyId, ProxyService proxyService) =>
        {
            var result = await proxyService.EnableProxyAsync(proxyId);
            return result.ToHttpResult();
        }).WithSummary("启用代理");

        group.MapPost("/{proxyId}/disable", async (Guid proxyId, ProxyService proxyService) =>
        {
            var result = await proxyService.DisableProxyAsync(proxyId);
            return result.ToHttpResult();
        }).WithSummary("禁用代理");

        group.MapPost("/{proxyId}/health-check", async (Guid proxyId, ProxyService proxyService) =>
        {
            var result = await proxyService.HealthCheckProxyAsync(proxyId);
            return result.ToHttpResult();
        }).WithSummary("健康检查代理");

        group.MapPost("/health-check", async (ProxyService proxyService) =>
        {
            var result = await proxyService.HealthCheckAllProxiesAsync();
            return result.ToHttpResult();
        }).WithSummary("健康检查所有代理");
    }

    public static void MapAdminContentEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/content").RequireAuthorization("AdminOnly").WithTags("Admin Content Management");

        group.MapGet("/", async (int page, int pageSize, string? type, string? search, bool? isBlacklisted, YouTubeService youTubeService) =>
        {
            var result = await youTubeService.GetContentAsync(page, pageSize, type, search, isBlacklisted);
            return result.ToHttpResult();
        }).WithSummary("获取内容列表");

        group.MapPost("/{contentType}/{contentId}/blacklist", async (string contentType, string contentId, YouTubeService youTubeService) =>
        {
            var result = await youTubeService.BlacklistContentAsync(contentType, contentId);
            return result.ToHttpResult();
        }).WithSummary("加入黑名单");

        group.MapPost("/{contentType}/{contentId}/unblacklist", async (string contentType, string contentId, YouTubeService youTubeService) =>
        {
            var result = await youTubeService.UnblacklistContentAsync(contentType, contentId);
            return result.ToHttpResult();
        }).WithSummary("移出黑名单");
    }
}