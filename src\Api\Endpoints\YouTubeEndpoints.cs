using Api.Extensions;
using Api.Services;

namespace Api.Endpoints;

public static class YouTubeEndpoints
{
    public static void MapYouTubeEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/youtube").RequireAuthorization().WithTags("YouTube");

        group.MapGet("/video/{videoId}", async (string videoId, YouTubeService youTubeService) =>
        {
            var result = await youTubeService.GetVideoInfoAsync(videoId);
            return result.ToHttpResult();
        }).WithSummary("获取视频信息");
    }
}