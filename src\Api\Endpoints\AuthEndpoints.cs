using System.Security.Claims;
using Api.Data.Models;
using Api.Extensions;
using Api.Filters;
using Api.Middleware;
using Api.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class AuthEndpoints
{
    public static void MapAuthEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/auth").WithTags("Auth");

        group.MapPost("/register", async (RegisterRequest request, HttpContext context, AuthService authService) =>
        {
            var anonymousUserId = context.GetUserId();
            var ipAddress = context.Connection.RemoteIpAddress?.ToString();
            var result = await authService.RegisterAsync(request, anonymousUserId, ipAddress);
            if (!result.IsSuccess) return result.ToHttpResult();

            await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            var user = result.Data!;
            await SignInUserAsync(context, user);
            await authService.RecordLoginSessionAsync(user.Id, ipAddress, context.Request.Headers.UserAgent.ToString());

            var userResponse = user.ToResponse();
            return TypedResults.Ok(ApiResponse<UserResponse>.Success(userResponse));
        }).WithValidation<RegisterRequest>().WithSummary("用户注册");

        group.MapPost("/login", async (LoginRequest request, HttpContext context, AuthService authService) =>
        {
            var ipAddress = context.Connection.RemoteIpAddress?.ToString();
            var result = await authService.LoginAsync(request, ipAddress);
            if (!result.IsSuccess) return result.ToHttpResult();

            var user = result.Data!;
            await SignInUserAsync(context, user);
            await authService.RecordLoginSessionAsync(user.Id, ipAddress, context.Request.Headers.UserAgent.ToString());

            var userResponse = user.ToResponse();
            return TypedResults.Ok(ApiResponse<UserResponse>.Success(userResponse));
        }).WithValidation<LoginRequest>().WithSummary("用户登录");

        group.MapPost("/forgot-password", async (ForgotPasswordRequest request, AuthService authService) =>
        {
            var result = await authService.ForgotPasswordAsync(request);
            return result.ToHttpResult();
        }).WithValidation<ForgotPasswordRequest>().WithSummary("忘记密码");

        group.MapPost("/reset-password", async (ResetPasswordRequest request, AuthService authService) =>
        {
            var result = await authService.ResetPasswordAsync(request);
            return result.ToHttpResult();
        }).WithValidation<ResetPasswordRequest>().WithSummary("重置密码");

        group.MapPost("/verify-email", async (VerifyEmailRequest request, AuthService authService) =>
        {
            var result = await authService.VerifyEmailAsync(request);
            return result.ToHttpResult();
        }).WithValidation<VerifyEmailRequest>().WithSummary("验证邮箱地址");

        group.MapPost("/logout", async (HttpContext context, AuthService authService) =>
        {
            var result = await authService.LogoutAsync(context.GetUserId());
            if (result.IsSuccess) await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            return result.ToHttpResult();
        }).RequireAuthorization("RegisteredUserOnly").WithSummary("用户登出");

        group.MapPost("/change-password", async (ChangePasswordRequest request, HttpContext context, AuthService authService) =>
        {
            var result = await authService.ChangePasswordAsync(context.GetUserId(), request);
            return result.ToHttpResult();
        }).RequireAuthorization("RegisteredUserOnly").WithValidation<ChangePasswordRequest>().WithSummary("修改密码");

        group.MapPost("/resend-verification-email", async (HttpContext context, AuthService authService) =>
        {
            var result = await authService.ResendVerificationEmailAsync(context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization("RegisteredUserOnly").WithSummary("重发邮箱验证邮件");

        group.MapGet("/me", (HttpContext context) =>
        {
            var userId = context.GetUserId();
            var email = context.User.FindFirstValue(ClaimTypes.Email);
            var userType = Enum.Parse<UserType>(context.User.FindFirstValue("UserType")!);
            var planType = Enum.Parse<UserPlanType>(context.User.FindFirstValue("PlanType")!);
            var currentUser = new CurrentUser(userId, userType, email, planType, userType == UserType.Anonymous);
            return TypedResults.Ok(ApiResponse<CurrentUser>.Success(currentUser));
        }).RequireAuthorization().WithSummary("获取当前用户信息");
    }

    private static async Task SignInUserAsync(HttpContext context, User user)
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new(ClaimTypes.Name, user.Email ?? user.Id.ToString()),
            new(ClaimTypes.Email, user.Email ?? string.Empty),
            new("UserType", user.UserType.ToString()),
            new("PlanType", user.PlanType.ToString()),
            new("ver", user.SecurityVersion.ToString())
        };

        var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
        var authProperties = new AuthenticationProperties
        {
            IsPersistent = true,
            ExpiresUtc = DateTimeOffset.UtcNow.AddDays(7)
        };

        await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity), authProperties);
    }
}