using Api.Data.Models;
using Shared.DTOs;

namespace Api.Extensions;

public static class EntityMappingExtensions
{
    public static UserResponse ToResponse(this User user)
    {
        return new UserResponse(user.Id, user.UserType, user.Email, user.PlanType, user.PlanExpiresAt, user.Status, user.CreatedAt, user.LastActiveAt);
    }

    public static ProxyDetailResponse ToDetailResponse(this Proxy proxy)
    {
        return new ProxyDetailResponse(proxy.Id, proxy.Host, proxy.Port, proxy.Username, proxy.ProxyType, proxy.Status, proxy.HealthStatus, proxy.LastUsedAt,
            proxy.LastHealthCheckAt, proxy.ResponseTimeMs, proxy.FailureCount, proxy.SuccessCount, proxy.UsageCount, proxy.ErrorMessage, proxy.CreatedAt,
            proxy.UpdatedAt, proxy.Notes);
    }

    public static WorkerResponse ToResponse(this Worker worker)
    {
        var hardwareInfo = worker.CpuCores.HasValue && worker.TotalMemoryGB.HasValue && worker.TotalDiskGB.HasValue
            ? new WorkerHardwareInfo(worker.CpuCores.Value, worker.TotalMemoryGB.Value, worker.TotalDiskGB.Value)
            : null;

        return new WorkerResponse(worker.Id, worker.Name, worker.BaseUrl, worker.MachineName, worker.Status, worker.HealthStatus, hardwareInfo,
            worker.TotalProcessedTasks, worker.ConsecutiveFailures, worker.CreatedAt, worker.UpdatedAt, worker.LastActiveAt, worker.LastHealthCheckAt,
            worker.LastFailureAt, worker.LastStartAt);
    }
}