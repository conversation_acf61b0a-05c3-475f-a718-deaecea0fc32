using Api.Data;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.Messages;

namespace Api.Services;

public class TaskPublishService
{
    private readonly AppDbContext _dbContext;
    private readonly ILogger<TaskPublishService> _logger;
    private readonly IPublishEndpoint _publishEndpoint;

    public TaskPublishService(AppDbContext dbContext, IPublishEndpoint publishEndpoint, ILogger<TaskPublishService> logger)
    {
        _dbContext = dbContext;
        _publishEndpoint = publishEndpoint;
        _logger = logger;
    }

    public async Task<ServiceResult> PublishTaskAsync(Guid taskId)
    {
        try
        {
            var task = await _dbContext.WorkerTasks.Where(wt => wt.Id == taskId).FirstOrDefaultAsync();

            if (task == null)
                return ServiceResult.Failure("任务不存在", "TASK_NOT_FOUND");

            if (task.Status != WorkerTaskStatus.Pending)
                return ServiceResult.Failure("任务状态不正确，只能发布待处理的任务");

            var taskMessage = new TaskMessage(task.Id, task.UserId, task.TaskType, task.Priority, task.VideoId, task.VideoUrl, task.OutputFormat, task.Quality,
                task.StartTime, task.EndTime, task.CreatedAt);

            await _publishEndpoint.Publish<ITaskMessage>(taskMessage);

            task.Status = WorkerTaskStatus.Queued;
            task.UpdatedAt = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已发布任务到队列: {TaskId}", taskId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布任务 {TaskId} 时发生错误", taskId);
            return ServiceResult.Failure("发布任务失败");
        }
    }

    public async Task<ServiceResult> PublishTaskResultAsync(TaskResultMessage resultMessage)
    {
        try
        {
            await _publishEndpoint.Publish<ITaskResultMessage>(resultMessage);
            _logger.LogInformation("已发布任务结果: {TaskId} - {Status}", resultMessage.TaskId, resultMessage.Status);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布任务结果 {TaskId} 时发生错误", resultMessage.TaskId);
            return ServiceResult.Failure("发布任务结果失败");
        }
    }

    public async Task<ServiceResult> PublishTaskProgressAsync(TaskProgressMessage progressMessage)
    {
        try
        {
            await _publishEndpoint.Publish<ITaskProgressMessage>(progressMessage);
            _logger.LogDebug("已发布任务进度: {TaskId} - {Progress}%", progressMessage.TaskId, progressMessage.Progress);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布任务进度 {TaskId} 时发生错误", progressMessage.TaskId);
            return ServiceResult.Failure("发布任务进度失败");
        }
    }

    public async Task<ServiceResult<int>> PublishPendingTasksAsync(int maxCount = 10)
    {
        try
        {
            var pendingTasks = await _dbContext.WorkerTasks.Where(wt => wt.Status == WorkerTaskStatus.Pending).OrderBy(wt => wt.Priority)
                .ThenBy(wt => wt.CreatedAt).Take(maxCount).ToListAsync();

            var publishedCount = 0;
            foreach (var task in pendingTasks)
            {
                var result = await PublishTaskAsync(task.Id);
                if (result.IsSuccess)
                    publishedCount++;
            }

            _logger.LogInformation("已发布 {Count} 个待处理任务", publishedCount);
            return ServiceResult<int>.Success(publishedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量发布任务时发生错误");
            return ServiceResult<int>.Failure("批量发布任务失败");
        }
    }
}