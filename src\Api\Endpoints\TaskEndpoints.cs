using Api.Extensions;
using Api.Middleware;
using Api.Services;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class TaskEndpoints
{
    public static void MapTaskEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/tasks").RequireAuthorization().WithTags("Tasks");

        group.MapPost("/video", async (CreateVideoTaskRequest request, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.CreateVideoTaskAsync(request, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("创建视频下载任务");

        group.MapPost("/audio", async (CreateAudioTaskRequest request, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.CreateAudioTaskAsync(request, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("创建音频提取任务");

        group.MapPost("/gif", async (CreateGifTaskRequest request, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.CreateGifTaskAsync(request, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("创建GIF制作任务");

        group.MapPost("/ringtone", async (CreateRingtoneTaskRequest request, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.CreateRingtoneTaskAsync(request, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("创建铃声制作任务");

        group.MapPost("/subtitle", async (CreateSubtitleTaskRequest request, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.CreateSubtitleTaskAsync(request, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("创建字幕下载任务");

        group.MapPost("/thumbnail", async (CreateThumbnailTaskRequest request, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.CreateThumbnailTaskAsync(request, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("创建缩略图下载任务");

        group.MapPost("/batch", async (CreateBatchTaskRequest request, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.CreateBatchTaskAsync(request, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("创建批量下载任务");

        group.MapGet("/", async (int page, int pageSize, WorkerTaskStatus? status, WorkerTaskType? type, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.GetUserTasksAsync(context.GetUserId(), page, pageSize);
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("获取任务列表");

        group.MapGet("/{taskId}", async (Guid taskId, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.GetTaskAsync(taskId, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("获取任务详情");

        group.MapGet("/batch", async (int page, int pageSize, BatchTaskStatus? status, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.GetUserTasksAsync(context.GetUserId(), page, pageSize);
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("获取批量任务列表");

        group.MapGet("/batch/{batchTaskId}", async (Guid batchTaskId, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.GetBatchTaskAsync(batchTaskId, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("获取批量任务详情");

        group.MapGet("/statistics", async (HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.GetUserTaskStatsAsync(context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("获取任务统计");

        group.MapPost("/{taskId}/cancel", async (Guid taskId, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.CancelTaskAsync(taskId, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("取消任务");

        group.MapPost("/{taskId}/retry", async (Guid taskId, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.RetryTaskAsync(taskId, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("重试任务");

        group.MapPost("/{taskId}/delete", async (Guid taskId, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.DeleteCompletedTaskAsync(taskId, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("删除任务");

        group.MapPost("/batch/{batchTaskId}/cancel", async (Guid batchTaskId, HttpContext context, TaskService taskService) =>
        {
            var userId = context.GetUserId();
            var result = await taskService.CancelBatchTaskAsync(batchTaskId, userId);
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("取消批量任务");

        group.MapPost("/batch/{batchTaskId}/pause", async (Guid batchTaskId, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.PauseBatchTaskAsync(batchTaskId, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("暂停批量任务");

        group.MapPost("/batch/{batchTaskId}/resume", async (Guid batchTaskId, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.ResumeBatchTaskAsync(batchTaskId, context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("恢复批量任务");

        group.MapPost("/cleanup", async (int daysOld, HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.CleanupCompletedTasksAsync(context.GetUserId(), daysOld);
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("清理已完成任务");

        group.MapPost("/cancel-all", async (HttpContext context, TaskService taskService) =>
        {
            var result = await taskService.CancelAllUserTasksAsync(context.GetUserId());
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("取消所有任务");
    }
}
