using FluentValidation;
using Shared.Common;

namespace Shared.DTOs;

// --- Request DTOs ---

public record WorkerRegisterRequest(string BaseUrl, string? MachineName, WorkerHardwareInfo HardwareInfo, DateTime LastStartAt);

public record WorkerHeartbeat(WorkerStatus Status, WorkerRuntimeMetrics RuntimeMetrics, DateTime Timestamp);

// --- Response DTOs ---

public record WorkerResponse(
    Guid Id,
    string Name,
    string BaseUrl,
    string? MachineName,
    WorkerStatus Status,
    WorkerHealthStatus HealthStatus,
    WorkerHardwareInfo? HardwareInfo,
    int TotalProcessedTasks,
    int ConsecutiveFailures,
    DateTime CreatedAt,
    DateTime UpdatedAt,
    DateTime? LastActiveAt,
    DateTime? LastHealthCheckAt,
    DateTime? LastFailureAt,
    DateTime? LastStartAt);

public record WorkerListResponse(List<WorkerResponse> Workers, int TotalCount, int Page, int PageSize);

public record WorkerDetailResponse(
    Guid Id,
    string Name,
    string BaseUrl,
    string? MachineName,
    int? CpuCores,
    double? TotalMemoryGB,
    double? TotalDiskGB,
    WorkerStatus Status,
    WorkerHealthStatus HealthStatus,
    int TotalProcessedTasks,
    int ConsecutiveFailures,
    DateTime? LastActiveAt,
    DateTime CreatedAt,
    List<WorkerMetricResponse> RecentMetrics,
    List<WorkerTaskSummary> ActiveTasks);

public record WorkerMetricResponse(
    double CpuUsagePercent,
    double MemoryUsagePercent,
    double DiskUsagePercent,
    double NetworkReceivedGB,
    double NetworkSentGB,
    double NetworkBandwidthMbps,
    int ActiveConnections,
    DateTime RecordedAt);

public record WorkerTaskSummary(Guid Id, string Name, WorkerTaskStatus Status, double Progress, DateTime CreatedAt);

public record WorkerStatsResponse(
    int TotalWorkers,
    int OnlineWorkers,
    int OfflineWorkers,
    int HealthyWorkers,
    int WarningWorkers,
    int CriticalWorkers,
    long TotalProcessedTasks,
    long TotalFailures);

public record WorkerPerformanceResponse(double AverageCpuUsage, double AverageMemoryUsage, double AverageDiskUsage, int TotalActiveTasks, DateTime LastUpdated);

public record WorkerDeletionSummary(Guid WorkerId, string WorkerName, int DeletedTasks, int DeletedMetrics, int DeletedAlerts, bool Forced);

public record WorkerRestartResult(Guid WorkerId, string WorkerName, string Message, DateTime Timestamp);

// --- Other DTOs ---

public record WorkerHardwareInfo(int CpuCores, double TotalMemoryGB, double TotalDiskGB);

public record WorkerRuntimeMetrics(
    double CpuUsagePercent,
    double MemoryUsagePercent,
    double DiskUsagePercent,
    int ActiveTasks,
    int TotalProcessedTasks,
    double NetworkReceivedGB,
    double NetworkSentGB,
    double NetworkBandwidthMbps,
    int ActiveConnections);

public record WorkerHealthCheckResult(int HealthyCount, int TotalCount);

public class WorkerRegisterRequestValidator : AbstractValidator<WorkerRegisterRequest>
{
    public WorkerRegisterRequestValidator()
    {
        RuleFor(x => x.BaseUrl).NotEmpty().WithMessage("BaseUrl 不能为空。").Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out _))
            .WithMessage("BaseUrl 必须是有效的URL。");
        RuleFor(x => x.HardwareInfo).NotNull().WithMessage("硬件信息不能为空。");
        RuleFor(x => x.HardwareInfo.CpuCores).GreaterThan(0).WithMessage("CPU核心数必须大于0。");
        RuleFor(x => x.HardwareInfo.TotalMemoryGB).GreaterThan(0).WithMessage("总内存必须大于0。");
        RuleFor(x => x.HardwareInfo.TotalDiskGB).GreaterThan(0).WithMessage("总磁盘空间必须大于0。");
    }
}

public class WorkerHeartbeatValidator : AbstractValidator<WorkerHeartbeat>
{
    public WorkerHeartbeatValidator()
    {
        RuleFor(x => x.Status).IsInEnum().WithMessage("无效的工作节点状态。");
        RuleFor(x => x.Timestamp).NotEmpty().WithMessage("时间戳不能为空。");
        RuleFor(x => x.RuntimeMetrics).NotNull().WithMessage("运行时指标不能为空。");
        RuleFor(x => x.RuntimeMetrics.CpuUsagePercent).InclusiveBetween(0, 100).WithMessage("CPU使用率必须在0到100之间。");
        RuleFor(x => x.RuntimeMetrics.MemoryUsagePercent).InclusiveBetween(0, 100).WithMessage("内存使用率必须在0到100之间。");
        RuleFor(x => x.RuntimeMetrics.DiskUsagePercent).InclusiveBetween(0, 100).WithMessage("磁盘使用率必须在0到100之间。");
    }
}