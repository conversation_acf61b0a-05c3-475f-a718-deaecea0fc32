using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Api.Data;
using Api.Data.Models;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class UserService(AppDbContext dbContext, ILogger<UserService> logger)
{
    public async Task<ServiceResult<User?>> GetUserByIdAsync(Guid userId)
    {
        var user = await dbContext.Users.FindAsync(userId);
        return ServiceResult<User?>.Success(user);
    }

    public async Task<ServiceResult<User?>> GetUserByEmailAsync(string email)
    {
        var normalizedEmail = email.Trim().ToLower();
        var user = await dbContext.Users.FirstOrDefaultAsync(u => u.Email == normalizedEmail && u.UserType == UserType.Registered);
        return ServiceResult<User?>.Success(user);
    }

    public async Task<ServiceResult<User>> CreateRegisteredUserAsync(string email, string password, string? ipAddress)
    {
        var normalizedEmail = email.Trim().ToLower();
        var now = DateTime.UtcNow;
        var emailVerificationToken = GenerateSecureToken();

        var newUser = new User
        {
            UserType = UserType.Registered,
            Email = normalizedEmail,
            PasswordHash = BCrypt.Net.BCrypt.HashPassword(password),
            PlanType = UserPlanType.Free,
            Status = UserAccountStatus.Active,
            EmailVerified = false,
            EmailVerificationToken = emailVerificationToken,
            EmailVerificationTokenExpiresAt = now.AddHours(24),
            LastLoginAt = now,
            LastLoginIp = ipAddress,
            LastActiveAt = now,
            SecurityVersion = 1
        };
        dbContext.Users.Add(newUser);
        await dbContext.SaveChangesAsync();
        logger.LogInformation("新注册用户创建成功: {Email}", email);
        return ServiceResult<User>.Success(newUser);
    }

    public async Task<ServiceResult<User>> ConvertAnonymousUserToRegisteredAsync(User anonymousUser, string email, string password, string? ipAddress)
    {
        var normalizedEmail = email.Trim().ToLower();
        var now = DateTime.UtcNow;
        var emailVerificationToken = GenerateSecureToken();

        anonymousUser.UserType = UserType.Registered;
        anonymousUser.Email = normalizedEmail;
        anonymousUser.PasswordHash = BCrypt.Net.BCrypt.HashPassword(password);
        anonymousUser.EmailVerified = false;
        anonymousUser.EmailVerificationToken = emailVerificationToken;
        anonymousUser.EmailVerificationTokenExpiresAt = now.AddHours(24);
        anonymousUser.LastLoginAt = now;
        anonymousUser.LastLoginIp = ipAddress;
        anonymousUser.LastActiveAt = now;
        IncrementSecurityVersion(anonymousUser);

        await dbContext.SaveChangesAsync();
        logger.LogInformation("匿名用户 {UserId} 已成功转换为注册用户: {Email}", anonymousUser.Id, email);
        return ServiceResult<User>.Success(anonymousUser);
    }

    public ServiceResult<bool> VerifyPassword(User user, string password)
    {
        if (string.IsNullOrEmpty(user.PasswordHash))
            return ServiceResult<bool>.Success(false);
        var isVerified = BCrypt.Net.BCrypt.Verify(password, user.PasswordHash);
        return ServiceResult<bool>.Success(isVerified);
    }

    public async Task<ServiceResult> RecordSuccessfulLoginAsync(User user, string? ipAddress)
    {
        user.LastLoginAt = DateTime.UtcNow;
        user.LastLoginIp = ipAddress;
        user.LastActiveAt = DateTime.UtcNow;
        user.LoginFailureCount = 0;
        user.AccountLockedUntil = null;
        await dbContext.SaveChangesAsync();
        return ServiceResult.Success();
    }

    public async Task<ServiceResult<bool>> RecordFailedLoginAttemptAsync(User user)
    {
        user.LoginFailureCount++;
        var isNowLocked = false;
        if (user.LoginFailureCount >= 5)
        {
            user.AccountLockedUntil = DateTime.UtcNow.AddMinutes(15);
            isNowLocked = true;
        }

        await dbContext.SaveChangesAsync();
        logger.LogWarning("用户 {UserId} 登录失败，失败次数: {FailureCount}，是否锁定: {IsLocked}", user.Id, user.LoginFailureCount, isNowLocked);
        return ServiceResult<bool>.Success(isNowLocked);
    }

    public async Task<ServiceResult> UpdatePasswordAsync(Guid userId, string newPassword)
    {
        var user = await dbContext.Users.FindAsync(userId);
        if (user is null)
            return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND));

        user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
        IncrementSecurityVersion(user);
        await dbContext.SaveChangesAsync();
        logger.LogInformation("用户 {UserId} 密码更新成功", userId);
        return ServiceResult.Success();
    }

    public async Task<ServiceResult<string>> GeneratePasswordResetTokenAsync(User user)
    {
        var resetToken = GenerateSecureToken();
        user.PasswordResetToken = resetToken;
        user.PasswordResetTokenExpiresAt = DateTime.UtcNow.AddHours(1);
        await dbContext.SaveChangesAsync();
        logger.LogInformation("为用户 {UserId} 生成了密码重置令牌", user.Id);
        return ServiceResult<string>.Success(resetToken);
    }

    public async Task<ServiceResult> ClearPasswordResetTokenAsync(User user)
    {
        user.PasswordResetToken = null;
        user.PasswordResetTokenExpiresAt = null;
        await dbContext.SaveChangesAsync();
        return ServiceResult.Success();
    }

    public async Task<ServiceResult<string>> GenerateEmailVerificationTokenAsync(User user)
    {
        var token = GenerateSecureToken();
        user.EmailVerificationToken = token;
        user.EmailVerificationTokenExpiresAt = DateTime.UtcNow.AddHours(24);
        await dbContext.SaveChangesAsync();
        logger.LogInformation("为用户 {UserId} 生成了新的邮箱验证令牌", user.Id);
        return ServiceResult<string>.Success(token);
    }

    public async Task<ServiceResult> SetEmailAsVerifiedAsync(User user)
    {
        user.EmailVerified = true;
        user.EmailVerificationToken = null;
        user.EmailVerificationTokenExpiresAt = null;
        await dbContext.SaveChangesAsync();
        logger.LogInformation("用户 {UserId} 的邮箱已标记为已验证", user.Id);
        return ServiceResult.Success();
    }

    public async Task<ServiceResult> UpdateUserStatusAsync(Guid userId, UserAccountStatus newStatus)
    {
        var user = await dbContext.Users.FindAsync(userId);
        if (user == null) return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND));

        if (user.Status == newStatus) return ServiceResult.Success();

        user.Status = newStatus;
        user.UpdatedAt = DateTime.UtcNow;

        if (newStatus == UserAccountStatus.Active)
        {
            user.AccountLockedUntil = null;
            user.LoginFailureCount = 0;
        }

        IncrementSecurityVersion(user);
        await dbContext.SaveChangesAsync();

        logger.LogInformation("用户 {UserId} 状态已更新为: {NewStatus}", userId, newStatus);
        return ServiceResult.Success();
    }

    public async Task<ServiceResult> UnlockUserAccountAsync(Guid userId)
    {
        var user = await dbContext.Users.FindAsync(userId);
        if (user == null) return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND));

        user.LoginFailureCount = 0;
        user.AccountLockedUntil = null;
        await dbContext.SaveChangesAsync();

        logger.LogInformation("账户已解锁: {UserId}", userId);
        return ServiceResult.Success();
    }

    public async Task<ServiceResult> RecordLoginSessionAsync(Guid userId, string? ipAddress, string? userAgent)
    {
        var session = new UserSession
        {
            UserId = userId,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            ExpiresAt = DateTime.UtcNow.AddDays(7)
        };

        dbContext.UserSessions.Add(session);
        await dbContext.SaveChangesAsync();
        logger.LogInformation("已为用户 {UserId} 记录新的登录会话: {SessionId}", userId, session.Id);
        return ServiceResult.Success();
    }

    public async Task<ServiceResult> RevokeUserSessionsAsync(Guid userId)
    {
        var user = await dbContext.Users.FindAsync(userId);
        if (user == null) return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND));

        IncrementSecurityVersion(user);
        await dbContext.SaveChangesAsync();

        logger.LogInformation("用户所有会话已通过提升安全版本来撤销: {UserId}", userId);
        return ServiceResult.Success();
    }

    public async Task<ServiceResult<User>> GetOrCreateAnonymousUserAsync(ClaimsPrincipal principal)
    {
        var userIdString = principal.FindFirstValue(ClaimTypes.NameIdentifier);
        if (!Guid.TryParse(userIdString, out var userId))
            return ServiceResult<User>.Failure(new Error(ErrorType.Validation, ErrorKeys.INVALID_CREDENTIALS,
                DeveloperMessage: $"ClaimsPrincipal 缺少有效的用户ID: '{userIdString}'。"));

        var user = await dbContext.Users.FindAsync(userId);
        if (user != null) return ServiceResult<User>.Success(user);

        var userTypeString = principal.FindFirstValue("UserType");
        if (Enum.TryParse<UserType>(userTypeString, out var userType) && userType == UserType.Anonymous)
        {
            var planTypeString = principal.FindFirstValue("PlanType");
            if (!Enum.TryParse<UserPlanType>(planTypeString, out var planType)) planType = UserPlanType.Free;

            var newUser = new User
            {
                Id = userId,
                UserType = UserType.Anonymous,
                PlanType = planType,
                Status = UserAccountStatus.Active,
                LastActiveAt = DateTime.UtcNow
            };

            dbContext.Users.Add(newUser);
            await dbContext.SaveChangesAsync();

            logger.LogInformation("匿名用户即时创建成功: {UserId}", userId);
            return ServiceResult<User>.Success(newUser);
        }

        return ServiceResult<User>.Failure(new Error(ErrorType.Unauthorized, ErrorKeys.INVALID_CREDENTIALS,
            DeveloperMessage: $"用户ID '{userId}' 在数据库中未找到，且无法作为匿名用户创建。可能为无效会话。"));
    }

    public async Task<ServiceResult<AdminUserListResponse>> GetUsersAsync(int page, int pageSize, string? search, UserType? userType, UserAccountStatus? status)
    {
        var query = dbContext.Users.AsNoTracking();

        if (!string.IsNullOrWhiteSpace(search)) query = query.Where(u => u.Email != null && u.Email.Contains(search));
        if (userType.HasValue) query = query.Where(u => u.UserType == userType.Value);
        if (status.HasValue) query = query.Where(u => u.Status == status.Value);

        var totalCount = await query.CountAsync();
        var skip = (page - 1) * pageSize;

        var users = await query.OrderByDescending(u => u.CreatedAt).Skip(skip).Take(pageSize).Select(u => new AdminUserListItem(u.Id, u.UserType, u.Email,
            u.PlanType, u.PlanExpiresAt, u.Status, u.EmailVerified, u.LastLoginAt, u.LastLoginIp, u.CreatedAt, u.LastActiveAt)).ToListAsync();

        var response = new AdminUserListResponse(users, totalCount, page, pageSize);
        return ServiceResult<AdminUserListResponse>.Success(response);
    }

    public async Task<ServiceResult<AdminUserDetailResponse>> GetUserDetailAsync(Guid userId)
    {
        var user = await dbContext.Users.AsNoTracking().FirstOrDefaultAsync(u => u.Id == userId);
        if (user == null) return ServiceResult<AdminUserDetailResponse>.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND));

        var totalTasks = await dbContext.WorkerTasks.CountAsync(t => t.UserId == userId);
        var completedTasks = await dbContext.WorkerTasks.CountAsync(t => t.UserId == userId && t.Status == WorkerTaskStatus.Completed);
        var failedTasks = await dbContext.WorkerTasks.CountAsync(t => t.UserId == userId && t.Status == WorkerTaskStatus.Failed);
        var activeSessions = await dbContext.UserSessions.CountAsync(s => s.UserId == userId && s.ExpiresAt > DateTime.UtcNow);
        var totalDownloadSize = await dbContext.WorkerTasks.Where(t => t.UserId == userId && t.FileSize.HasValue).SumAsync(t => t.FileSize!.Value);
        var lastTaskAt = await dbContext.WorkerTasks.Where(t => t.UserId == userId).OrderByDescending(t => t.CreatedAt).Select(t => (DateTime?)t.CreatedAt)
            .FirstOrDefaultAsync();

        var stats = new AdminUserStatsResponse(totalTasks, completedTasks, failedTasks, activeSessions, totalDownloadSize, lastTaskAt);

        var response = new AdminUserDetailResponse(user.Id, user.UserType, user.Email, user.PlanType, user.PlanExpiresAt, user.Status, user.EmailVerified,
            user.EmailVerificationToken, user.EmailVerificationTokenExpiresAt, user.PasswordResetToken, user.PasswordResetTokenExpiresAt, user.SecurityVersion,
            user.LoginFailureCount, user.AccountLockedUntil, user.LastLoginAt, user.LastLoginIp, user.CreatedAt, user.LastActiveAt, user.UpdatedAt, stats);

        return ServiceResult<AdminUserDetailResponse>.Success(response);
    }

    public async Task<ServiceResult<ResetPasswordResponse>> ResetUserPasswordAsync(Guid userId)
    {
        var user = await dbContext.Users.FindAsync(userId);
        if (user == null) return ServiceResult<ResetPasswordResponse>.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND));

        var newPassword = GenerateRandomPassword();
        user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
        user.UpdatedAt = DateTime.UtcNow;
        user.PasswordResetToken = null;
        user.PasswordResetTokenExpiresAt = null;
        IncrementSecurityVersion(user);

        await dbContext.SaveChangesAsync();

        var response = new ResetPasswordResponse(newPassword, DateTime.UtcNow);
        logger.LogInformation("管理员已重置用户密码: {UserId}", userId);
        return ServiceResult<ResetPasswordResponse>.Success(response);
    }

    public async Task<ServiceResult> UpdateUserPlanAsync(Guid userId, UpdateUserPlanRequest request)
    {
        var user = await dbContext.Users.FindAsync(userId);
        if (user == null) return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND));

        user.PlanType = request.PlanType;
        user.PlanExpiresAt = request.PlanExpiresAt;
        user.UpdatedAt = DateTime.UtcNow;

        await dbContext.SaveChangesAsync();

        logger.LogInformation("用户套餐已更新: {UserId}, 新套餐: {PlanType}", userId, request.PlanType);
        return ServiceResult.Success();
    }

    public async Task<ServiceResult<AdminUserSessionListResponse>> GetUserSessionsAsync(Guid userId)
    {
        try
        {
            var user = await dbContext.Users.AsNoTracking().FirstOrDefaultAsync(u => u.Id == userId);
            if (user == null)
                return ServiceResult<AdminUserSessionListResponse>.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));

            var sessions = await dbContext.UserSessions.Where(s => s.UserId == userId).OrderByDescending(s => s.CreatedAt).Select(s =>
                new AdminUserSessionResponse(s.Id, s.UserId, s.IpAddress, s.UserAgent, s.CreatedAt, s.ExpiresAt, s.ExpiresAt <= DateTime.UtcNow)).ToListAsync();

            var response = new AdminUserSessionListResponse(sessions, sessions.Count, 1, sessions.Count);
            return ServiceResult<AdminUserSessionListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户会话时发生错误，用户ID: {UserId}", userId);
            return ServiceResult<AdminUserSessionListResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR,
                DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<AdminUserTaskListResponse>> GetUserTasksAsync(Guid userId, int page, int pageSize)
    {
        try
        {
            var user = await dbContext.Users.AsNoTracking().FirstOrDefaultAsync(u => u.Id == userId);
            if (user == null)
                return ServiceResult<AdminUserTaskListResponse>.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));

            var skip = (page - 1) * pageSize;

            var workerTasks = await dbContext.WorkerTasks.Where(t => t.UserId == userId).OrderByDescending(t => t.CreatedAt).Skip(skip).Take(pageSize)
                .Select(t => new WorkerTaskResponse(t.Id, t.UserId, t.BatchTaskId, t.Name, t.TaskType, t.VideoId, t.VideoTitle, t.VideoUrl, t.Status,
                    t.Progress, t.Priority, t.OutputFormat, t.Quality, t.StartTime, t.EndTime, t.ResultPath, t.FileSize, t.ErrorMessage, t.CreatedAt,
                    t.StartedAt, t.CompletedAt, t.FileExpiresAt)).ToListAsync();

            var batchTasks = await dbContext.BatchTasks.Where(t => t.UserId == userId).OrderByDescending(t => t.CreatedAt).Skip(skip).Take(pageSize).Select(t =>
                    new BatchTaskResponse(t.Id, t.UserId, t.Name, t.SourceType, t.SourceId, t.SourceUrl, t.SourceTitle, t.TotalVideoCount, t.SelectedVideoCount,
                        t.Status, t.Progress, t.CompletedTaskCount, t.FailedTaskCount, t.CreatedAt, t.StartedAt, t.CompletedAt, new List<WorkerTaskResponse>()))
                .ToListAsync();

            var totalCount = await dbContext.WorkerTasks.CountAsync(t => t.UserId == userId) + await dbContext.BatchTasks.CountAsync(t => t.UserId == userId);

            var response = new AdminUserTaskListResponse(workerTasks, batchTasks, totalCount, page, pageSize);
            return ServiceResult<AdminUserTaskListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户任务时发生错误，用户ID: {UserId}", userId);
            return ServiceResult<AdminUserTaskListResponse>.Failure(
                new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<AdminUserContentListResponse>> GetUserContentHistoryAsync(Guid userId, int page, int pageSize)
    {
        try
        {
            var user = await dbContext.Users.AsNoTracking().FirstOrDefaultAsync(u => u.Id == userId);
            if (user == null)
                return ServiceResult<AdminUserContentListResponse>.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));

            var skip = (page - 1) * pageSize;

            var contentQuery = from task in dbContext.WorkerTasks
                where task.UserId == userId && !string.IsNullOrEmpty(task.VideoId)
                group task by task.VideoId
                into g
                select new AdminUserContentResponse(g.Key, g.First().VideoTitle, g.First().VideoUrl, null, g.Count(), g.Min(t => t.CreatedAt),
                    g.Max(t => t.CreatedAt));

            var totalCount = await contentQuery.CountAsync();
            var content = await contentQuery.OrderByDescending(c => c.LastTaskAt).Skip(skip).Take(pageSize).ToListAsync();

            var response = new AdminUserContentListResponse(content, totalCount, page, pageSize);
            return ServiceResult<AdminUserContentListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户内容记录时发生错误，用户ID: {UserId}", userId);
            return ServiceResult<AdminUserContentListResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR,
                DeveloperMessage: ex.Message));
        }
    }

    public async Task<ServiceResult<AdminUserBillingResponse>> GetUserBillingHistoryAsync(Guid userId)
    {
        try
        {
            var user = await dbContext.Users.AsNoTracking().FirstOrDefaultAsync(u => u.Id == userId);
            if (user == null)
                return ServiceResult<AdminUserBillingResponse>.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND, DeveloperMessage: "用户不存在"));

            var history = new List<AdminBillingHistoryResponse>();

            var response = new AdminUserBillingResponse(userId, user.PlanType, user.PlanExpiresAt, history);

            return ServiceResult<AdminUserBillingResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户计费记录时发生错误，用户ID: {UserId}", userId);
            return ServiceResult<AdminUserBillingResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR,
                DeveloperMessage: ex.Message));
        }
    }

    private void IncrementSecurityVersion(User user)
    {
        user.SecurityVersion++;
    }

    private static string GenerateSecureToken()
    {
        var bytes = RandomNumberGenerator.GetBytes(32);
        return Convert.ToBase64String(bytes).Replace('+', '-').Replace('/', '_').TrimEnd('=');
    }

    private static string GenerateRandomPassword()
    {
        const string validChars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789!@#$%^&*";
        const int passwordLength = 12;

        var password = new StringBuilder();
        var randomBytes = new byte[passwordLength];

        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(randomBytes);
        }

        foreach (var b in randomBytes) password.Append(validChars[b % validChars.Length]);

        return password.ToString();
    }

    public async Task<ServiceResult<int>> CleanupExpiredEmailTokensAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var expiredTokensCount = await dbContext.Users.Where(u => u.EmailVerificationTokenExpiresAt <= now && u.EmailVerificationToken != null)
                .ExecuteUpdateAsync(u => u
                    .SetProperty(x => x.EmailVerificationToken, (string?)null).SetProperty(x => x.EmailVerificationTokenExpiresAt, (DateTime?)null)
                    .SetProperty(x => x.UpdatedAt, now));

            logger.LogInformation("已清理 {Count} 个过期邮箱验证令牌", expiredTokensCount);
            return ServiceResult<int>.Success(expiredTokensCount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "清理过期邮箱验证令牌时发生错误");
            return ServiceResult<int>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "清理过期邮箱验证令牌失败"));
        }
    }

    public async Task<ServiceResult<int>> CleanupExpiredPasswordTokensAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var expiredTokensCount = await dbContext.Users.Where(u => u.PasswordResetTokenExpiresAt <= now && u.PasswordResetToken != null)
                .ExecuteUpdateAsync(u => u
                    .SetProperty(x => x.PasswordResetToken, (string?)null).SetProperty(x => x.PasswordResetTokenExpiresAt, (DateTime?)null)
                    .SetProperty(x => x.UpdatedAt, now));

            logger.LogInformation("已清理 {Count} 个过期密码重置令牌", expiredTokensCount);
            return ServiceResult<int>.Success(expiredTokensCount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "清理过期密码重置令牌时发生错误");
            return ServiceResult<int>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "清理过期密码重置令牌失败"));
        }
    }

    public async Task<ServiceResult<int>> CleanupInactiveAnonymousUsersAsync(int inactiveDays = 180)
    {
        try
        {
            var inactiveThreshold = DateTime.UtcNow.AddDays(-inactiveDays);
            var inactiveUsersCount = await dbContext.Users
                .Where(u => u.UserType == UserType.Anonymous && u.LastActiveAt < inactiveThreshold).ExecuteDeleteAsync();

            logger.LogInformation("已清理 {Count} 个不活跃匿名用户", inactiveUsersCount);
            return ServiceResult<int>.Success(inactiveUsersCount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "清理不活跃匿名用户时发生错误");
            return ServiceResult<int>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "清理不活跃匿名用户失败"));
        }
    }

    public async Task<ServiceResult<int>> CleanupExpiredSessionsAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var expiredSessionsCount = await dbContext.UserSessions.Where(s => s.ExpiresAt <= now).ExecuteDeleteAsync();

            logger.LogInformation("已清理 {Count} 个过期会话", expiredSessionsCount);
            return ServiceResult<int>.Success(expiredSessionsCount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "清理过期会话时发生错误");
            return ServiceResult<int>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "清理过期会话失败"));
        }
    }
}