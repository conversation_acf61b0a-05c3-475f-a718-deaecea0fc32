using Api.Extensions;
using Api.Middleware;
using Api.Services;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class FileEndpoints
{
    public static void MapFileEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/files").WithTags("Files");

        group.MapGet("/download/{taskId}", async (Guid taskId, HttpContext context, FileDownloadService fileDownloadService) =>
        {
            var userId = context.GetUserId();
            var result = await fileDownloadService.GenerateDownloadUrlAsync(taskId, userId);
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("生成下载链接");

        group.MapPost("/download/link", async (DownloadLinkRequest request, HttpContext context, FileDownloadService fileDownloadService) =>
        {
            var userId = context.GetUserId();
            var validFor = request.ValidFor ?? TimeSpan.FromHours(1);
            var result = await fileDownloadService.GenerateDownloadLinkAsync(request.TaskId, userId, validFor);
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("生成自定义下载链接");

        group.MapPost("/download/batch", async (BatchDownloadRequest request, HttpContext context, FileDownloadService fileDownloadService) =>
        {
            var userId = context.GetUserId();
            var results = new List<BatchDownloadResult>();

            foreach (var taskId in request.TaskIds)
            {
                var result = await fileDownloadService.GenerateDownloadUrlAsync(taskId, userId);
                var errorMessage = result.IsSuccess ? null : result.Error.DeveloperMessage ?? "生成下载链接失败";
                results.Add(new BatchDownloadResult(taskId, result.IsSuccess, result.Data, errorMessage));
            }

            var response = new BatchDownloadResponse(results, results.Count(r => r.Success), results.Count(r => !r.Success));
            var serviceResult = ServiceResult<BatchDownloadResponse>.Success(response);
            return serviceResult.ToHttpResult();
        }).RequireAuthorization().WithSummary("批量生成下载链接");

        group.MapGet("/info/{taskId}", async (Guid taskId, HttpContext context, FileDownloadService fileDownloadService) =>
        {
            var userId = context.GetUserId();
            var result = await fileDownloadService.GetFileInfoAsync(taskId, userId);
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("获取文件信息");

        group.MapGet("/stats", async (HttpContext context, FileDownloadService fileDownloadService) =>
        {
            var userId = context.GetUserId();
            var result = await fileDownloadService.GetFileStatsAsync(userId);
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("获取文件统计");

        group.MapGet("/history", async (int page, int pageSize, HttpContext context, FileDownloadService fileDownloadService) =>
        {
            var userId = context.GetUserId();
            page = Math.Max(1, page);
            pageSize = Math.Clamp(pageSize, 1, 100);

            var result = await fileDownloadService.GetDownloadHistoryAsync(userId, page, pageSize);
            return result.ToHttpResult();
        }).RequireAuthorization().WithSummary("获取下载历史");
    }
}
