using FluentValidation;
using Shared.Common;

namespace Shared.DTOs;

// --- Request DTOs ---

public record AddProxyRequest(string Host, int Port, string? Username, string? Password, ProxyType Type, string? Notes);

public record UpdateProxyRequest(string Host, int Port, string? Username, string? Password, ProxyType Type, string? Notes);

public record BatchAddProxiesRequest(List<AddProxyRequest> Proxies);

public record BatchDeleteProxiesRequest(List<Guid> ProxyIds);

// --- Response DTOs ---

public record ProxyListResponse(List<ProxyDetailResponse> Proxies, int TotalCount, int Page, int PageSize);

public record ProxyDetailResponse(
    Guid Id,
    string Host,
    int Port,
    string? Username,
    ProxyType Type,
    ProxyStatus Status,
    ProxyHealthStatus HealthStatus,
    DateTime? LastUsedAt,
    DateTime? LastHealthCheckAt,
    int? ResponseTimeMs,
    int FailureCount,
    int SuccessCount,
    int UsageCount,
    string? ErrorMessage,
    DateTime CreatedAt,
    DateTime UpdatedAt,
    string? Notes);

public record ProxyHealthCheckResponse(Guid ProxyId, bool IsHealthy, int? ResponseTimeMs, string? ErrorMessage, DateTime CheckedAt);

// --- Other DTOs ---

public record ProxyInfo(string Host, int Port, string? Username = null, string? Password = null, ProxyType Type = ProxyType.Http);

public record ProxyStats(int TotalProxies, int ActiveProxies, int HealthyProxies, int UnhealthyProxies, double HealthRate, DateTime LastHealthCheck);

// --- Validators ---

public class AddProxyRequestValidator : AbstractValidator<AddProxyRequest>
{
    public AddProxyRequestValidator()
    {
        RuleFor(x => x.Host).NotEmpty().WithMessage("主机地址不能为空。");
        RuleFor(x => x.Port).InclusiveBetween(1, 65535).WithMessage("端口号必须在 1 到 65535 之间。");
        RuleFor(x => x.Type).IsInEnum().WithMessage("无效的代理类型。");
    }
}

public class UpdateProxyRequestValidator : AbstractValidator<UpdateProxyRequest>
{
    public UpdateProxyRequestValidator()
    {
        RuleFor(x => x.Host).NotEmpty().WithMessage("主机地址不能为空。");
        RuleFor(x => x.Port).InclusiveBetween(1, 65535).WithMessage("端口号必须在 1 到 65535 之间。");
        RuleFor(x => x.Type).IsInEnum().WithMessage("无效的代理类型。");
    }
}

public class BatchAddProxiesRequestValidator : AbstractValidator<BatchAddProxiesRequest>
{
    public BatchAddProxiesRequestValidator()
    {
        RuleFor(x => x.Proxies).NotEmpty().WithMessage("代理列表不能为空。");
        RuleForEach(x => x.Proxies).SetValidator(new AddProxyRequestValidator());
    }
}

public class BatchDeleteProxiesRequestValidator : AbstractValidator<BatchDeleteProxiesRequest>
{
    public BatchDeleteProxiesRequestValidator()
    {
        RuleFor(x => x.ProxyIds).NotEmpty().WithMessage("代理ID列表不能为空。");
        RuleForEach(x => x.ProxyIds).NotEmpty().WithMessage("代理ID不能为空。");
    }
}