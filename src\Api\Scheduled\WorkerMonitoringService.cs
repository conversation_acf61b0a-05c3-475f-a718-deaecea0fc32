using Api.Data;
using Api.Data.Models;
using Api.Services;
using Microsoft.EntityFrameworkCore;
using Shared.Common;

namespace Api.Scheduled;

public class WorkerMonitoringService(IServiceProvider serviceProvider, ILogger<WorkerMonitoringService> logger) : BackgroundService
{
    private readonly TimeSpan _alertCooldown = TimeSpan.FromMinutes(30);
    private readonly Dictionary<Guid, DateTime> _lastAlertTimes = new();
    private readonly TimeSpan _monitoringInterval = TimeSpan.FromMinutes(2);

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        logger.LogInformation("工作节点监控服务已启动");

        while (!stoppingToken.IsCancellationRequested)
            try
            {
                await PerformMonitoringCheckAsync();
                await Task.Delay(_monitoringInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "工作节点监控服务中发生错误");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }

        logger.LogInformation("工作节点监控服务已停止");
    }

    private async Task PerformMonitoringCheckAsync()
    {
        using var scope = serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var workerService = scope.ServiceProvider.GetRequiredService<WorkerService>();

        await PerformHealthCheckAsync(workerService);
        await CheckOfflineWorkersAsync(dbContext);
        await CheckCriticalWorkersAsync(dbContext);
        await CleanupOldDataAsync(dbContext);
        CleanupAlertHistory();
    }

    private async Task PerformHealthCheckAsync(WorkerService workerService)
    {
        try
        {
            var result = await workerService.HealthCheckAllWorkersAsync();

            if (result.IsSuccess && result.Data != null)
                logger.LogDebug("工作节点健康检查完成。{HealthyCount}/{TotalCount} 个节点健康", result.Data.HealthyCount, result.Data.TotalCount);
            else
                logger.LogWarning("工作节点健康检查失败: {Error}", result.Error.DeveloperMessage);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "执行工作节点健康检查时发生错误");
        }
    }

    private async Task CheckOfflineWorkersAsync(AppDbContext dbContext)
    {
        var offlineThreshold = DateTime.UtcNow.AddMinutes(-5);

        var offlineWorkers = await dbContext.Workers
            .Where(w => w.Status != WorkerStatus.Offline && (w.LastActiveAt == null || w.LastActiveAt < offlineThreshold)).ToListAsync();

        foreach (var worker in offlineWorkers)
        {
            worker.Status = WorkerStatus.Offline;
            worker.HealthStatus = WorkerHealthStatus.Unknown;
            worker.UpdatedAt = DateTime.UtcNow;

            await SendWorkerOfflineAlertAsync(worker);

            logger.LogWarning("工作节点 {WorkerId} ({Name}) 离线", worker.Id, worker.Name);
        }

        if (offlineWorkers.Any())
            await dbContext.SaveChangesAsync();
    }

    private async Task CheckCriticalWorkersAsync(AppDbContext dbContext)
    {
        var criticalWorkers = await dbContext.Workers.Where(w => w.Status != WorkerStatus.Offline && w.ConsecutiveFailures >= 10).ToListAsync();

        foreach (var worker in criticalWorkers)
            await SendWorkerCriticalAlertAsync(worker);
    }

    private async Task SendWorkerOfflineAlertAsync(Worker worker)
    {
        if (!ShouldSendAlert(worker.Id))
            return;

        try
        {
            using var scope = serviceProvider.CreateScope();
            var emailService = scope.ServiceProvider.GetRequiredService<EmailService>();

            var adminEmail = GetAdminEmail();
            if (string.IsNullOrEmpty(adminEmail))
                return;

            var subject = $"[告警] 工作节点离线 - {worker.Name}";
            var body = $@"
                <h2>工作节点离线告警</h2>
                <p><strong>节点信息：</strong></p>
                <ul>
                    <li>节点ID: {worker.Id}</li>
                    <li>节点名称: {worker.Name}</li>
                    <li>基础URL: {worker.BaseUrl}</li>
                    <li>最后活跃时间: {worker.LastActiveAt:yyyy-MM-dd HH:mm:ss}</li>
                    <li>失败次数: {worker.ConsecutiveFailures}</li>
                </ul>
                <p><strong>建议操作：</strong></p>
                <ul>
                    <li>检查节点服务器状态</li>
                    <li>检查网络连接</li>
                    <li>重启工作节点服务</li>
                </ul>
                <p>告警时间: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC</p>
            ";

            await emailService.SendEmailAsync(adminEmail, subject, body);
            _lastAlertTimes[worker.Id] = DateTime.UtcNow;

            await CreateOfflineAlertAsync(worker);

            logger.LogInformation("已发送工作节点离线告警: {WorkerId}", worker.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "发送工作节点 {WorkerId} 离线告警时发生错误", worker.Id);
        }
    }

    private async Task SendWorkerCriticalAlertAsync(Worker worker)
    {
        if (!ShouldSendAlert(worker.Id))
            return;

        try
        {
            using var scope = serviceProvider.CreateScope();
            var emailService = scope.ServiceProvider.GetRequiredService<EmailService>();

            var adminEmail = GetAdminEmail();
            if (string.IsNullOrEmpty(adminEmail))
                return;

            var subject = $"[严重告警] 工作节点故障 - {worker.Name}";
            var body = $@"
                <h2>工作节点严重故障告警</h2>
                <p><strong>节点信息：</strong></p>
                <ul>
                    <li>节点ID: {worker.Id}</li>
                    <li>节点名称: {worker.Name}</li>
                    <li>基础URL: {worker.BaseUrl}</li>
                    <li>失败次数: {worker.ConsecutiveFailures}</li>
                    <li>最后失败时间: {worker.LastFailureAt:yyyy-MM-dd HH:mm:ss}</li>
                    <li>健康状态: {worker.HealthStatus}</li>
                </ul>
                <p><strong>故障详情：</strong></p>
                <p>该节点连续失败次数已达到 {worker.ConsecutiveFailures} 次，可能存在严重故障。</p>
                <p><strong>建议操作：</strong></p>
                <ul>
                    <li>立即检查节点服务器状态</li>
                    <li>查看节点日志文件</li>
                    <li>检查系统资源使用情况</li>
                    <li>考虑重启或替换节点</li>
                </ul>
                <p>告警时间: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC</p>
            ";

            await emailService.SendEmailAsync(adminEmail, subject, body);
            _lastAlertTimes[worker.Id] = DateTime.UtcNow;

            await CreateCriticalAlertAsync(worker);

            logger.LogWarning("已发送工作节点严重故障告警: {WorkerId}", worker.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "发送工作节点 {WorkerId} 严重故障告警时发生错误", worker.Id);
        }
    }

    private async Task CleanupOldDataAsync(AppDbContext dbContext)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-7);
        await dbContext.WorkerMetrics.Where(m => m.RecordedAt < cutoffDate).ExecuteDeleteAsync();
        await dbContext.WorkerAlerts.Where(a => a.IsResolved && a.ResolvedAt < cutoffDate.AddDays(-30)).ExecuteDeleteAsync();

        logger.LogDebug("已清理 {CutoffDate} 之前的旧数据", cutoffDate);
    }

    private async Task CreateOfflineAlertAsync(Worker worker)
    {
        try
        {
            using var scope = serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var existingAlert = await dbContext.WorkerAlerts.Where(a => a.WorkerId == worker.Id && a.AlertType == WorkerAlertType.NodeOffline && !a.IsResolved)
                .FirstOrDefaultAsync();

            if (existingAlert != null)
                return;

            var alert = new WorkerAlert
            {
                Id = Guid.NewGuid(),
                WorkerId = worker.Id,
                AlertType = WorkerAlertType.NodeOffline,
                AlertLevel = WorkerAlertLevel.Critical,
                Title = "工作节点离线",
                Message = $"工作节点 {worker.Name} 已离线，最后活跃时间: {worker.LastActiveAt:yyyy-MM-dd HH:mm:ss}",
                CreatedAt = DateTime.UtcNow
            };

            dbContext.WorkerAlerts.Add(alert);
            await dbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "创建工作节点 {WorkerId} 离线告警时发生错误", worker.Id);
        }
    }

    private async Task CreateCriticalAlertAsync(Worker worker)
    {
        try
        {
            using var scope = serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var existingAlert = await dbContext.WorkerAlerts.Where(a => a.WorkerId == worker.Id && a.AlertType == WorkerAlertType.TaskFailure && !a.IsResolved)
                .FirstOrDefaultAsync();

            if (existingAlert != null)
                return;

            var alert = new WorkerAlert
            {
                Id = Guid.NewGuid(),
                WorkerId = worker.Id,
                AlertType = WorkerAlertType.TaskFailure,
                AlertLevel = WorkerAlertLevel.Critical,
                Title = "工作节点严重故障",
                Message = $"工作节点 {worker.Name} 失败次数达到 {worker.ConsecutiveFailures} 次，可能存在严重故障",
                CreatedAt = DateTime.UtcNow
            };

            dbContext.WorkerAlerts.Add(alert);
            await dbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "创建工作节点 {WorkerId} 严重故障告警时发生错误", worker.Id);
        }
    }

    private bool ShouldSendAlert(Guid workerId)
    {
        if (!_lastAlertTimes.TryGetValue(workerId, out var lastAlertTime))
            return true;

        return DateTime.UtcNow - lastAlertTime > _alertCooldown;
    }

    private static string GetAdminEmail()
    {
        return Environment.GetEnvironmentVariable("ADMIN_EMAIL") ?? "";
    }

    private void CleanupAlertHistory()
    {
        var cutoffTime = DateTime.UtcNow.AddHours(-24);
        var keysToRemove = _lastAlertTimes.Where(kvp => kvp.Value < cutoffTime).Select(kvp => kvp.Key).ToList();

        foreach (var key in keysToRemove)
            _lastAlertTimes.Remove(key);
    }
}