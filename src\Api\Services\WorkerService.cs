using Api.Data;
using Api.Data.Models;
using Api.Extensions;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class WorkerService(AppDbContext dbContext, HttpClient httpClient, ILogger<WorkerService> logger)
{
    public async Task<ServiceResult<WorkerResponse>> RegisterWorkerAsync(WorkerRegisterRequest request)
    {
        await using var transaction = await dbContext.Database.BeginTransactionAsync();
        try
        {
            var existingWorker = await dbContext.Workers.FirstOrDefaultAsync(w => w.BaseUrl == request.BaseUrl);
            if (existingWorker != null)
            {
                existingWorker.MachineName = request.MachineName;
                existingWorker.CpuCores = request.HardwareInfo.CpuCores;
                existingWorker.TotalMemoryGB = request.HardwareInfo.TotalMemoryGB;
                existingWorker.TotalDiskGB = request.HardwareInfo.TotalDiskGB;
                existingWorker.Status = WorkerStatus.Online;
                existingWorker.HealthStatus = WorkerHealthStatus.Healthy;
                existingWorker.LastActiveAt = DateTime.UtcNow;
                existingWorker.LastStartAt = request.LastStartAt;

                await dbContext.SaveChangesAsync();
                await transaction.CommitAsync();

                logger.LogInformation("工作节点重新注册成功: {WorkerId} - {BaseUrl}", existingWorker.Id, request.BaseUrl);

                return ServiceResult<WorkerResponse>.Success(existingWorker.ToResponse());
            }

            var worker = new Worker
            {
                Id = Guid.NewGuid(),
                Name = $"Worker-{request.MachineName ?? "Unknown"}",
                BaseUrl = request.BaseUrl,
                MachineName = request.MachineName,
                CpuCores = request.HardwareInfo.CpuCores,
                TotalMemoryGB = request.HardwareInfo.TotalMemoryGB,
                TotalDiskGB = request.HardwareInfo.TotalDiskGB,
                Status = WorkerStatus.Online,
                HealthStatus = WorkerHealthStatus.Healthy,
                LastActiveAt = DateTime.UtcNow,
                LastStartAt = request.LastStartAt
            };

            dbContext.Workers.Add(worker);
            await dbContext.SaveChangesAsync();
            await transaction.CommitAsync();

            logger.LogInformation("新工作节点注册成功: {WorkerId} - {BaseUrl}", worker.Id, request.BaseUrl);

            return ServiceResult<WorkerResponse>.Success(worker.ToResponse());
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            logger.LogError(ex, "注册工作节点时发生错误: {BaseUrl}", request.BaseUrl);
            return ServiceResult<WorkerResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "注册工作节点失败"));
        }
    }

    public async Task<ServiceResult> UpdateHeartbeatAsync(Guid workerId, WorkerHeartbeat heartbeat)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null)
            {
                logger.LogWarning("收到未知工作节点的心跳: {WorkerId}", workerId);
                return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "工作节点不存在"));
            }

            worker.Status = heartbeat.Status;
            worker.LastActiveAt = heartbeat.Timestamp;

            var metrics = new WorkerMetrics
            {
                Id = Guid.NewGuid(),
                WorkerId = workerId,
                CpuUsagePercent = heartbeat.RuntimeMetrics.CpuUsagePercent,
                MemoryUsagePercent = heartbeat.RuntimeMetrics.MemoryUsagePercent,
                DiskUsagePercent = heartbeat.RuntimeMetrics.DiskUsagePercent,
                ActiveTasks = heartbeat.RuntimeMetrics.ActiveTasks,
                TotalProcessedTasks = heartbeat.RuntimeMetrics.TotalProcessedTasks,
                NetworkReceivedGB = heartbeat.RuntimeMetrics.NetworkReceivedGB,
                NetworkSentGB = heartbeat.RuntimeMetrics.NetworkSentGB,
                NetworkBandwidthMbps = heartbeat.RuntimeMetrics.NetworkBandwidthMbps,
                ActiveConnections = heartbeat.RuntimeMetrics.ActiveConnections,
                RecordedAt = heartbeat.Timestamp
            };

            dbContext.WorkerMetrics.Add(metrics);

            worker.HealthStatus = DetermineHealthStatus(heartbeat.RuntimeMetrics);

            await dbContext.SaveChangesAsync();

            logger.LogDebug("工作节点心跳更新成功: {WorkerId}", workerId);

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "更新工作节点心跳时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "更新心跳失败"));
        }
    }

    public async Task<ServiceResult> UnregisterWorkerAsync(Guid workerId)
    {
        await using var transaction = await dbContext.Database.BeginTransactionAsync();
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null) return ServiceResult.Success();

            worker.Status = WorkerStatus.Offline;

            await dbContext.SaveChangesAsync();
            await transaction.CommitAsync();

            logger.LogInformation("工作节点已注销: {WorkerId}", workerId);

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            logger.LogError(ex, "注销工作节点时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "注销工作节点失败"));
        }
    }

    public async Task<ServiceResult<List<WorkerResponse>>> GetWorkerListAsync()
    {
        try
        {
            var workers = await dbContext.Workers.AsNoTracking().OrderBy(w => w.Name).ToListAsync();
            return ServiceResult<List<WorkerResponse>>.Success(workers.Select(w => w.ToResponse()).ToList());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取工作节点列表时发生错误");
            return ServiceResult<List<WorkerResponse>>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "获取工作节点列表失败"));
        }
    }

    public async Task<ServiceResult<WorkerDetailResponse>> GetWorkerByIdAsync(Guid workerId)
    {
        try
        {
            var worker = await dbContext.Workers.Include(w => w.Metrics.OrderByDescending(m => m.RecordedAt).Take(10))
                .Include(w => w.AssignedTasks.Where(t => t.Status == WorkerTaskStatus.Processing)).AsNoTracking().FirstOrDefaultAsync(w => w.Id == workerId);

            if (worker == null)
                return ServiceResult<WorkerDetailResponse>.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "工作节点不存在"));

            var recentMetrics = worker.Metrics.Select(m => new WorkerMetricResponse(m.CpuUsagePercent, m.MemoryUsagePercent, m.DiskUsagePercent,
                m.NetworkReceivedGB, m.NetworkSentGB, m.NetworkBandwidthMbps, m.ActiveConnections, m.RecordedAt)).ToList();

            var activeTasks = worker.AssignedTasks.Select(t => new WorkerTaskSummary(t.Id, t.Name, t.Status, t.Progress, t.CreatedAt)).ToList();

            var response = new WorkerDetailResponse(worker.Id, worker.Name, worker.BaseUrl, worker.MachineName, worker.CpuCores, worker.TotalMemoryGB,
                worker.TotalDiskGB, worker.Status, worker.HealthStatus, worker.TotalProcessedTasks, worker.ConsecutiveFailures, worker.LastActiveAt,
                worker.CreatedAt, recentMetrics, activeTasks);

            return ServiceResult<WorkerDetailResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取工作节点详情时发生错误: {WorkerId}", workerId);
            return ServiceResult<WorkerDetailResponse>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "获取工作节点详情失败"));
        }
    }

    public async Task<ServiceResult<int>> GetActiveTaskCountAsync(Guid workerId)
    {
        try
        {
            var activeTaskCount = await dbContext.WorkerTasks.AsNoTracking()
                .Where(t => t.WorkerId == workerId && (t.Status == WorkerTaskStatus.Processing || t.Status == WorkerTaskStatus.Queued)).CountAsync();

            return ServiceResult<int>.Success(activeTaskCount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取工作节点活跃任务数时发生错误: {WorkerId}", workerId);
            return ServiceResult<int>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "获取活跃任务数失败"));
        }
    }

    public async Task<ServiceResult> EnableWorkerAsync(Guid workerId)
    {
        return await UpdateWorkerStatusAsync(workerId, WorkerStatus.Online);
    }

    public async Task<ServiceResult> DisableWorkerAsync(Guid workerId)
    {
        return await UpdateWorkerStatusAsync(workerId, WorkerStatus.Offline);
    }

    public async Task<ServiceResult> RestartWorkerAsync(Guid workerId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null) return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "工作节点不存在"));

            // TODO: Implement actual restart command to worker via HTTP/RPC
            logger.LogInformation("重启命令已发送到工作节点: {WorkerId}", workerId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "重启工作节点时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "重启工作节点失败"));
        }
    }

    public async Task<ServiceResult> HealthCheckWorkerAsync(Guid workerId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null) return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "工作节点不存在"));

            await PerformHealthCheckAsync(worker);
            await dbContext.SaveChangesAsync();

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "健康检查工作节点时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "健康检查工作节点失败"));
        }
    }

    public async Task<ServiceResult<WorkerHealthCheckResult>> HealthCheckAllWorkersAsync()
    {
        try
        {
            var workers = await dbContext.Workers.ToListAsync();
            foreach (var worker in workers) await PerformHealthCheckAsync(worker);

            await dbContext.SaveChangesAsync();

            // 统计健康节点数量
            var healthyCount = await dbContext.Workers.CountAsync(w => w.HealthStatus == WorkerHealthStatus.Healthy);
            var totalCount = workers.Count;
            var result = new WorkerHealthCheckResult(healthyCount, totalCount);

            logger.LogInformation("已完成所有工作节点健康检查，{HealthyCount}/{TotalCount} 个节点健康", healthyCount, totalCount);
            return ServiceResult<WorkerHealthCheckResult>.Success(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "批量健康检查时发生错误");
            return ServiceResult<WorkerHealthCheckResult>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "批量健康检查失败"));
        }
    }

    public async Task<ServiceResult<int>> ResetInactiveWorkerFailuresAsync(int inactiveDays = 7)
    {
        try
        {
            var now = DateTime.UtcNow;
            var workerResetThreshold = now.AddDays(-inactiveDays);

            var resetWorkerFailuresCount = await dbContext.Workers.Where(w => w.LastActiveAt < workerResetThreshold && w.ConsecutiveFailures > 0)
                .ExecuteUpdateAsync(w => w.SetProperty(x => x.ConsecutiveFailures, 0).SetProperty(x => x.UpdatedAt, now));

            logger.LogInformation("已重置 {Count} 个不活跃工作节点的失败计数", resetWorkerFailuresCount);
            return ServiceResult<int>.Success(resetWorkerFailuresCount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "重置不活跃工作节点失败计数时发生错误");
            return ServiceResult<int>.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "重置不活跃工作节点失败计数失败"));
        }
    }

    public async Task<ServiceResult> CleanupWorkerFilesAsync(Guid workerId)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null) return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "工作节点不存在"));

            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
            var response = await httpClient.PostAsync($"{worker.BaseUrl}/cleanup", null, cts.Token);

            if (!response.IsSuccessStatusCode)
                return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.EXTERNAL_SERVICE_ERROR,
                    DeveloperMessage: $"清理文件请求失败，状态码: {response.StatusCode}"));

            logger.LogInformation("工作节点文件清理完成: {WorkerId}", workerId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "清理工作节点文件时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "清理文件失败"));
        }
    }

    public async Task<ServiceResult> CleanupAllWorkerFilesAsync()
    {
        try
        {
            var workers = await dbContext.Workers.Where(w => w.Status == WorkerStatus.Online).ToListAsync();

            var cleanupTasks = workers.Select(async worker =>
            {
                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                    var response = await httpClient.PostAsync($"{worker.BaseUrl}/cleanup", null, cts.Token);
                    if (!response.IsSuccessStatusCode) logger.LogWarning("清理工作节点 {WorkerId} 文件失败，状态码: {StatusCode}", worker.Id, response.StatusCode);
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "清理工作节点 {WorkerId} 文件失败", worker.Id);
                }
            }).ToList();

            await Task.WhenAll(cleanupTasks);

            logger.LogInformation("已完成所有工作节点文件清理");
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "批量清理文件时发生错误");
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "批量清理文件失败"));
        }
    }

    public async Task<ServiceResult> DeleteWorkerAsync(Guid workerId)
    {
        await using var transaction = await dbContext.Database.BeginTransactionAsync();
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null) return ServiceResult.Success();

            var activeTasks = await dbContext.WorkerTasks.AsNoTracking()
                .Where(t => t.WorkerId == workerId && (t.Status == WorkerTaskStatus.Processing || t.Status == WorkerTaskStatus.Queued)).CountAsync();

            if (activeTasks > 0)
                return ServiceResult.Failure(new Error(ErrorType.Conflict, ErrorKeys.CONFLICT, DeveloperMessage: "工作节点有活跃任务，无法删除"));

            dbContext.Workers.Remove(worker);
            await dbContext.SaveChangesAsync();
            await transaction.CommitAsync();

            logger.LogInformation("工作节点已删除: {WorkerId}", workerId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            logger.LogError(ex, "删除工作节点时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "删除工作节点失败"));
        }
    }

    public async Task<ServiceResult<YouTubeVideoResponse>> FetchYouTubeVideoAsync(string videoId)
    {
        
    }

    private async Task<ServiceResult> UpdateWorkerStatusAsync(Guid workerId, WorkerStatus newStatus)
    {
        try
        {
            var worker = await dbContext.Workers.FindAsync(workerId);
            if (worker == null) return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.NOT_FOUND, DeveloperMessage: "工作节点不存在"));

            if (worker.Status == newStatus) return ServiceResult.Success();

            worker.Status = newStatus;
            worker.UpdatedAt = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();

            logger.LogInformation("工作节点 {WorkerId} 状态已更新为: {NewStatus}", workerId, newStatus);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "更新工作节点状态时发生错误: {WorkerId}", workerId);
            return ServiceResult.Failure(new Error(ErrorType.Internal, ErrorKeys.INTERNAL_SERVER_ERROR, DeveloperMessage: "更新工作节点状态失败"));
        }
    }

    private WorkerHealthStatus DetermineHealthStatus(WorkerRuntimeMetrics metrics)
    {
        if (metrics.CpuUsagePercent > 90 || metrics.MemoryUsagePercent > 90 || metrics.DiskUsagePercent > 95)
            return WorkerHealthStatus.Critical;
        if (metrics.CpuUsagePercent > 70 || metrics.MemoryUsagePercent > 70 || metrics.DiskUsagePercent > 80)
            return WorkerHealthStatus.Warning;
        return WorkerHealthStatus.Healthy;
    }

    private async Task PerformHealthCheckAsync(Worker worker, CancellationToken cancellationToken = default)
    {
        try
        {
            using var cts = CancellationTokenSource.CreateLinkedTokenSource(new CancellationTokenSource(TimeSpan.FromSeconds(10)).Token, cancellationToken);
            var response = await httpClient.GetAsync($"{worker.BaseUrl}/health", cts.Token);

            worker.HealthStatus = response.IsSuccessStatusCode ? WorkerHealthStatus.Healthy : WorkerHealthStatus.Critical;
            worker.LastHealthCheckAt = DateTime.UtcNow;
            worker.UpdatedAt = DateTime.UtcNow;

            if (response.IsSuccessStatusCode)
            {
                if (worker.Status == WorkerStatus.Offline)
                {
                    worker.Status = WorkerStatus.Online;
                    logger.LogInformation("工作节点重新上线: {WorkerId}", worker.Id);
                }
            }
            else
            {
                logger.LogWarning("工作节点健康检查失败: {WorkerId}, 状态码: {StatusCode}", worker.Id, response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            worker.HealthStatus = WorkerHealthStatus.Critical;
            worker.Status = WorkerStatus.Offline;
            logger.LogError(ex, "工作节点健康检查异常: {WorkerId}", worker.Id);
        }
    }
}