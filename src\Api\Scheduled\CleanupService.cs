using Api.Services;

namespace Api.Scheduled;

public class CleanupService(IServiceProvider serviceProvider, ILogger<CleanupService> logger) : BackgroundService
{
    private readonly TimeSpan _cleanupInterval = TimeSpan.FromHours(1);

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        logger.LogInformation("清理服务已启动");

        while (!stoppingToken.IsCancellationRequested)
            try
            {
                await PerformCleanupAsync();
                await Task.Delay(_cleanupInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "清理操作时发生错误");
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }

        logger.LogInformation("清理服务已停止");
    }

    private async Task PerformCleanupAsync()
    {
        using var scope = serviceProvider.CreateScope();
        var userService = scope.ServiceProvider.GetRequiredService<UserService>();
        var taskService = scope.ServiceProvider.GetRequiredService<TaskService>();
        var workerService = scope.ServiceProvider.GetRequiredService<WorkerService>();
        var adminService = scope.ServiceProvider.GetRequiredService<AdminService>();

        // 清理过期会话
        var expiredSessionsResult = await userService.CleanupExpiredSessionsAsync();
        var expiredSessionsCount = expiredSessionsResult.IsSuccess ? expiredSessionsResult.Data : 0;

        // 清理过期邮箱验证令牌
        var expiredEmailTokensResult = await userService.CleanupExpiredEmailTokensAsync();
        var expiredEmailTokensCount = expiredEmailTokensResult.IsSuccess ? expiredEmailTokensResult.Data : 0;

        // 清理过期密码重置令牌
        var expiredPasswordTokensResult = await userService.CleanupExpiredPasswordTokensAsync();
        var expiredPasswordTokensCount = expiredPasswordTokensResult.IsSuccess ? expiredPasswordTokensResult.Data : 0;

        // 清理不活跃匿名用户
        var inactiveAnonymousUsersResult = await userService.CleanupInactiveAnonymousUsersAsync();
        var inactiveAnonymousUsersCount = inactiveAnonymousUsersResult.IsSuccess ? inactiveAnonymousUsersResult.Data : 0;

        // 清理过期任务文件
        var expiredTasksResult = await taskService.CleanupExpiredTaskFilesAsync();
        var expiredTasksCount = expiredTasksResult.IsSuccess ? expiredTasksResult.Data : 0;

        // 重置不活跃工作节点失败计数
        var resetWorkerFailuresResult = await workerService.ResetInactiveWorkerFailuresAsync();
        var resetWorkerFailuresCount = resetWorkerFailuresResult.IsSuccess ? resetWorkerFailuresResult.Data : 0;

        // 清理过期监控数据和告警记录
        await adminService.CleanupOldDataAsync();

        var totalCleaned = expiredSessionsCount + expiredEmailTokensCount + expiredPasswordTokensCount + inactiveAnonymousUsersCount + expiredTasksCount +
                           resetWorkerFailuresCount;
        if (totalCleaned > 0)
            logger.LogInformation("清理完成。总共清理项目: {Count}", totalCleaned);
    }
}