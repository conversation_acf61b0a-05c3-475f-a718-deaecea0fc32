using Api.Data.Models;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class AuthService(UserService userService, EmailService emailService, ILogger<AuthService> logger)
{
    public async Task<ServiceResult<User>> RegisterAsync(RegisterRequest request, Guid? anonymousUserId, string? ipAddress)
    {
        var existingUserResult = await userService.GetUserByEmailAsync(request.Email);
        if (!existingUserResult.IsSuccess) return ServiceResult<User>.Failure(existingUserResult.Error!);
        if (existingUserResult.Data != null)
            return ServiceResult<User>.Failure(new Error(ErrorType.Conflict, ErrorKeys.EMAIL_ALREADY_REGISTERED,
                new Dictionary<string, object> { { "email", request.Email } }));

        ServiceResult<User> userResult;
        if (anonymousUserId.HasValue)
        {
            var anonUserResult = await userService.GetUserByIdAsync(anonymousUserId.Value);
            if (anonUserResult.IsSuccess && anonUserResult.Data?.UserType == UserType.Anonymous)
                userResult = await userService.ConvertAnonymousUserToRegisteredAsync(anonUserResult.Data, request.Email, request.Password, ipAddress);
            else
                userResult = await userService.CreateRegisteredUserAsync(request.Email, request.Password, ipAddress);
        }
        else
        {
            userResult = await userService.CreateRegisteredUserAsync(request.Email, request.Password, ipAddress);
        }

        if (!userResult.IsSuccess) return userResult;

        try
        {
            var user = userResult.Data!;
            var tokenResult = await userService.GenerateEmailVerificationTokenAsync(user);
            if (tokenResult.IsSuccess) await emailService.SendEmailVerificationAsync(user.Email!, tokenResult.Data!);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "发送邮箱验证邮件失败: {Email}，但用户已创建。", userResult.Data!.Email);
        }

        return userResult;
    }

    public async Task<ServiceResult<User>> LoginAsync(LoginRequest request, string? ipAddress)
    {
        var userResult = await userService.GetUserByEmailAsync(request.Email);
        if (!userResult.IsSuccess) return ServiceResult<User>.Failure(userResult.Error!);
        if (userResult.Data == null) return ServiceResult<User>.Failure(new Error(ErrorType.Unauthorized, ErrorKeys.INVALID_CREDENTIALS));

        var user = userResult.Data;

        if (user.Status != UserAccountStatus.Active)
            return ServiceResult<User>.Failure(new Error(ErrorType.Forbidden, ErrorKeys.ACCOUNT_STATUS_INACTIVE,
                new Dictionary<string, object> { { "status", user.Status.ToString() } }));

        if (user.AccountLockedUntil.HasValue && user.AccountLockedUntil.Value > DateTime.UtcNow)
            return ServiceResult<User>.Failure(new Error(ErrorType.Forbidden, ErrorKeys.ACCOUNT_LOCKED,
                new Dictionary<string, object> { { "lockedUntil", user.AccountLockedUntil?.ToString("o") ?? string.Empty } }));

        var passwordResult = userService.VerifyPassword(user, request.Password);
        if (!passwordResult.IsSuccess || !passwordResult.Data)
        {
            var lockResult = await userService.RecordFailedLoginAttemptAsync(user);
            if (lockResult.IsSuccess && lockResult.Data)
                return ServiceResult<User>.Failure(new Error(ErrorType.Forbidden, ErrorKeys.ACCOUNT_LOCKED_TOO_MANY_ATTEMPTS,
                    new Dictionary<string, object> { { "lockedUntil", user.AccountLockedUntil?.ToString("o") ?? string.Empty } }));
            return ServiceResult<User>.Failure(new Error(ErrorType.Unauthorized, ErrorKeys.INVALID_CREDENTIALS));
        }

        await userService.RecordSuccessfulLoginAsync(user, ipAddress);

        return ServiceResult<User>.Success(user);
    }

    public async Task<ServiceResult> ForgotPasswordAsync(ForgotPasswordRequest request)
    {
        var userResult = await userService.GetUserByEmailAsync(request.Email);
        if (userResult.IsSuccess && userResult.Data != null)
        {
            var user = userResult.Data;
            var tokenResult = await userService.GeneratePasswordResetTokenAsync(user);
            if (tokenResult.IsSuccess)
                try
                {
                    await emailService.SendPasswordResetAsync(user.Email!, tokenResult.Data!);
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "发送密码重置邮件失败: {Email}", user.Email);
                }
        }

        return ServiceResult.Success();
    }

    public async Task<ServiceResult> ResetPasswordAsync(ResetPasswordRequest request)
    {
        var userResult = await userService.GetUserByEmailAsync(request.Email);
        if (userResult.IsSuccess && userResult.Data != null)
        {
            var user = userResult.Data;
            if (user.PasswordResetToken == request.Token && user.PasswordResetTokenExpiresAt >= DateTime.UtcNow)
            {
                await userService.UpdatePasswordAsync(user.Id, request.NewPassword);
                await userService.ClearPasswordResetTokenAsync(user);
            }
        }

        return ServiceResult.Success();
    }

    public async Task<ServiceResult> VerifyEmailAsync(VerifyEmailRequest request)
    {
        var userResult = await userService.GetUserByEmailAsync(request.Email);
        if (!userResult.IsSuccess || userResult.Data == null)
            return ServiceResult.Failure(new Error(ErrorType.Unauthorized, ErrorKeys.EMAIL_VERIFICATION_TOKEN_INVALID_OR_EXPIRED));

        var user = userResult.Data;
        if (user.EmailVerificationToken != request.Token || user.EmailVerificationTokenExpiresAt < DateTime.UtcNow)
            return ServiceResult.Failure(new Error(ErrorType.Unauthorized, ErrorKeys.EMAIL_VERIFICATION_TOKEN_INVALID_OR_EXPIRED));

        return await userService.SetEmailAsVerifiedAsync(user);
    }

    public async Task<ServiceResult> LogoutAsync(Guid userId)
    {
        return await userService.RevokeUserSessionsAsync(userId);
    }

    public async Task<ServiceResult> ChangePasswordAsync(Guid userId, ChangePasswordRequest request)
    {
        var userResult = await userService.GetUserByIdAsync(userId);
        if (!userResult.IsSuccess || userResult.Data == null)
            return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND));

        var user = userResult.Data;
        var passwordResult = userService.VerifyPassword(user, request.CurrentPassword);

        if (!passwordResult.IsSuccess || !passwordResult.Data)
            return ServiceResult.Failure(new Error(ErrorType.Unauthorized, ErrorKeys.CURRENT_PASSWORD_MISMATCH));

        var updateResult = await userService.UpdatePasswordAsync(userId, request.NewPassword);
        if (!updateResult.IsSuccess) return updateResult;

        try
        {
            await emailService.SendPasswordChangedAsync(user.Email!);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "发送密码更改通知邮件失败: {Email}", user.Email);
        }

        return ServiceResult.Success();
    }

    public async Task<ServiceResult> ResendVerificationEmailAsync(Guid userId)
    {
        var userResult = await userService.GetUserByIdAsync(userId);
        if (!userResult.IsSuccess || userResult.Data == null)
            return ServiceResult.Failure(new Error(ErrorType.NotFound, ErrorKeys.USER_NOT_FOUND));

        var user = userResult.Data;
        if (user.EmailVerified) return ServiceResult.Failure(new Error(ErrorType.Conflict, ErrorKeys.EMAIL_ALREADY_VERIFIED));

        var tokenResult = await userService.GenerateEmailVerificationTokenAsync(user);
        if (!tokenResult.IsSuccess) return ServiceResult.Failure(tokenResult.Error!);

        try
        {
            await emailService.SendEmailVerificationAsync(user.Email!, tokenResult.Data!);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "为用户 {UserId} 重新发送邮箱验证邮件失败", userId);
        }

        return ServiceResult.Success();
    }

    public async Task<ServiceResult> RecordLoginSessionAsync(Guid userId, string? ipAddress, string? userAgent)
    {
        return await userService.RecordLoginSessionAsync(userId, ipAddress, userAgent);
    }
}