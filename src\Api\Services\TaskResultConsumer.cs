using Api.Data;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.Messages;

namespace Api.Services;

public class TaskResultConsumer(AppDbContext dbContext, ILogger<TaskResultConsumer> logger) : IConsumer<ITaskResultMessage>
{
    public async Task Consume(ConsumeContext<ITaskResultMessage> context)
    {
        var message = context.Message;

        try
        {
            var task = await dbContext.WorkerTasks.Where(wt => wt.Id == message.TaskId).FirstOrDefaultAsync();

            if (task == null)
            {
                logger.LogWarning("未找到任务结果消息对应的任务: {TaskId}", message.TaskId);
                return;
            }

            task.Status = message.Status;
            task.Progress = message.Progress;
            task.ErrorMessage = message.ErrorMessage;
            task.OutputFilePath = message.OutputFilePath;
            task.FileSize = message.FileSize;
            task.WorkerNodeId = message.WorkerNodeId;
            task.UpdatedAt = DateTime.UtcNow;

            if (message.Status == WorkerTaskStatus.Processing && task.StartedAt == null)
                task.StartedAt = DateTime.UtcNow;

            if (message.Status is WorkerTaskStatus.Completed or WorkerTaskStatus.Failed or WorkerTaskStatus.Cancelled)
                task.CompletedAt = message.CompletedAt;

            await dbContext.SaveChangesAsync();

            if (task.BatchTaskId.HasValue)
                await UpdateBatchTaskProgressAsync(task.BatchTaskId.Value);

            logger.LogInformation("已处理任务结果: {TaskId} - {Status}", message.TaskId, message.Status);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "处理任务结果 {TaskId} 时发生错误", message.TaskId);
            throw;
        }
    }

    private async Task UpdateBatchTaskProgressAsync(Guid batchTaskId)
    {
        try
        {
            var batchTask = await dbContext.BatchTasks.Where(bt => bt.Id == batchTaskId).FirstOrDefaultAsync();

            if (batchTask == null) return;

            var taskStats = await dbContext.WorkerTasks.Where(wt => wt.BatchTaskId == batchTaskId).GroupBy(wt => 1).Select(g => new
            {
                Total = g.Count(),
                Completed = g.Count(wt => wt.Status == WorkerTaskStatus.Completed),
                Failed = g.Count(wt => wt.Status == WorkerTaskStatus.Failed),
                Processing = g.Count(wt => wt.Status == WorkerTaskStatus.Processing),
                Cancelled = g.Count(wt => wt.Status == WorkerTaskStatus.Cancelled)
            }).FirstOrDefaultAsync();

            if (taskStats == null) return;

            batchTask.CompletedTaskCount = taskStats.Completed;
            batchTask.FailedTaskCount = taskStats.Failed;
            batchTask.CancelledTaskCount = taskStats.Cancelled;
            batchTask.Progress = taskStats.Total > 0 ? taskStats.Completed * 100 / taskStats.Total : 0;
            batchTask.UpdatedAt = DateTime.UtcNow;

            if (taskStats.Completed + taskStats.Failed + taskStats.Cancelled == taskStats.Total)
            {
                batchTask.Status = taskStats.Failed > 0 ? BatchTaskStatus.Failed :
                    taskStats.Cancelled == taskStats.Total ? BatchTaskStatus.Cancelled : BatchTaskStatus.Completed;
                batchTask.CompletedAt = DateTime.UtcNow;
            }
            else if (taskStats.Processing > 0)
            {
                batchTask.Status = BatchTaskStatus.Running;
            }

            await dbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "更新批量任务进度 {BatchTaskId} 时发生错误", batchTaskId);
        }
    }
}

public class TaskProgressConsumer : IConsumer<ITaskProgressMessage>
{
    private readonly AppDbContext _dbContext;
    private readonly ILogger<TaskProgressConsumer> _logger;

    public TaskProgressConsumer(AppDbContext dbContext, ILogger<TaskProgressConsumer> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<ITaskProgressMessage> context)
    {
        var message = context.Message;

        try
        {
            await _dbContext.WorkerTasks.Where(wt => wt.Id == message.TaskId).ExecuteUpdateAsync(wt =>
                wt.SetProperty(x => x.Progress, message.Progress).SetProperty(x => x.UpdatedAt, DateTime.UtcNow));

            _logger.LogDebug("已更新任务进度: {TaskId} - {Progress}%", message.TaskId, message.Progress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理任务进度 {TaskId} 时发生错误", message.TaskId);
        }
    }
}